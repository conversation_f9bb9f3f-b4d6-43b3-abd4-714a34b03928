import React, { useEffect, useState } from "react";
import axios from "axios";
import sectionstyles from "./../css/BranchSection19.module.css";
import removestyle from "./../css/SectionRemoveModal.module.css";
import { useNavigate } from "react-router-dom";
import ErrorImage from "./../img/warning-sign-9745.svg";
import dashboardContentStyles from "./../css/dashboardContent.module.css";
import sectionDataGet from "../API/grade-section-mapping-controller/section-data";
import gradeSectionGet from "../API/grade-section-mapping-controller/grade_section_get";
import gradeListGet from "../API/grade-section-mapping-controller/grade_get";
import sectionListGet from "../API/grade-section-mapping-controller/section_get";
import createGradeSectionMapping from "../API/grade-section-mapping-controller/grade_section_post";
import BranchControllerBoardIdGet from "../API/grade-section-mapping-controller/branch_Id_get_boardId";
import BoardNameGet from "../API/grade-section-mapping-controller/boardName_from_boardId";
import gradeSectionMappingUpdate from "../API/grade-section-mapping-controller/grade_section_mapping_put";
import gradeSectionMappingToggle from "../API/grade-section-mapping-controller/grade_section_mapping_toggle";
import School_name from "./school_name"; //school name changer
import Branches_Changer_School_Id from "../API/branch-controller/Branches_Changer_School_Id";
import { computeHeadingLevel } from "@testing-library/react";
import EditIconButton from "../../components/common/EditIconButton";
import DeleteIconButton from "../../components/common/DeleteIconButton";
import GreenAddButton from "../../components/common/GreenAddButton";
import HoverGreyCancelButton from "../../components/common/HoverGreyCancelButton";
import BlueButton from "../../components/common/BlueButton";
import CommonDeleteModal from "./CommonDeleteModal";
import MenuBookOutlinedIcon from "@mui/icons-material/MenuBookOutlined";
import { ControlPoint } from "@mui/icons-material";
import { SearchOutlined } from "@mui/icons-material";
import AddCircleOutlineOutlinedIcon from "@mui/icons-material/AddCircleOutlineOutlined";
// import gradeSection_get_id from "../API_CONTROLLER/grade-section-controller/gradeSection_get_id";
import Switch from "../../components/common/Switch";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import dashHome from "../../../src/img/dashHome.svg";
import searchIcn from "../../../src/img/search.svg";

import {
  Checkbox,
  FormControl,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Paper,
  Chip,
  Input,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  InputAdornment,
  Tooltip,
} from "@mui/material";
import teacheridstyles from "../css/TeacherId.module.css";

const GradeSectionMapping = (props) => {
  const [showModal, setShowModal] = useState(false);
  const [toggle, setToggle] = useState(false);
  const [tempNewSection, setTempNewSection] = useState([]);

  const grades = props.grades;
  const sections = props.sections;
  const gradeSection = props.gradeSection;
  const addGradeRow = props.addGradeRow;
  const newGradeSectionMapping = props.newGradeSectionMapping;
  const setNewSection = props.setNewSection;
  const newSection = props.newSection;
  const updateGrade = props.updateGrade;
  const handleUpdate = props.handleUpdate;
  const handleToggle = props.handleToggle;

  const addMappingNew = props.addMappingNew;
  const [addModal, setModal] = useState(false);
  const [newSetion, setnewSection] = useState("");

  const handleNewSection = (e) => {
    if (e.target.value === "ADD_NEW") {
      setShowModal(true);
    } else {
      if (e.target.value) {
        setNewSection([e.target.value]);
        // setTempNewSection(e.target.value)
      } else {
        setModal(true);
      }
    }
  };

  const addSection = () => {
    var data = {
      section: newSetion,
      discription: newSetion,
    };
    return axios
      .post(
        `${process.env.REACT_APP_SWAGGER_URL}v1/api/master/sections`,
        data,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${sessionStorage.getItem("token")}`,
          },
        }
      )
      .then((response) => {
        // console.log("response.data.id----->", response.data.data.id);
        setNewSection([response.data.data.id]);
        setTempNewSection([response.data.data.id]);
        // setModal(false);
      })
      .catch((error) => {
        // console.log(error);
        setModal(false);
      });
  };

  const fetchData = async (pageSize) => {
    const response = await axios.get(
      `${process.env.REACT_APP_SWAGGER_URL}v1/api/user/grade-section?schoolId=2c918083811d6bcd01811ec834b10000&branchId=2c9180838126f94a0181283e58040009`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${sessionStorage.getItem("token")}`,
        },
      }
    );
    if (response?.data?.data) {
      const tableData = response.data.data;
      const totalElement = response?.data?.totalElements;
      if (totalElement >= pageSize) {
        // setPageSize(totalElement);
      }

      // setUpdatedData(response?.data?.data);
    }
  };

  const existingGrades = gradeSection.map((grade_section) => {
    return grade_section.gradeId;
  });

  [grades].forEach((grade) => {
    if (!existingGrades.indexOf(grade.id)) {
    }
  });

  return (
    <>
      <div
        className={teacheridstyles.flex_container_teacherid}
        id={teacheridstyles.modal_teacherid}
        style={addModal ? { display: "flex" } : { display: "none" }}
      >
        <div
          className={teacheridstyles.main_content_teacherid}
          id={teacheridstyles.modal_content_teacherid}
        >
          <h5 className={teacheridstyles.head}>Add a New Section</h5>
          <div style={{ marginLeft: "35px" }}>
            <input
              type="text"
              name="PinCode"
              id="PinCode"
              placeholder="Add Section"
              style={{
                width: "250px",
                height: "30px",
                borderRadius: "5px",
              }}
              value={newSetion}
              onChange={(event) => setnewSection(event.target.value)}
            />
          </div>
          <div className={teacheridstyles.modal_buttons_teacherid}>
            <button
              className={teacheridstyles.ok_btn}
              id={teacheridstyles.ok_btn_1_teacherid}
              style={{ borderRadius: "0px" }}
              onClick={addSection}
            >
              Add
            </button>
            <button
              className={teacheridstyles.ok_btn}
              id={teacheridstyles.ok_btn_1_teacherid}
              style={{ borderRadius: "0px" }}
              onClick={() => setModal(false)}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
      <div className={sectionstyles.section_list_table}>
        <table className={sectionstyles.table_section}>
          <thead>
            <tr className={sectionstyles.table_head_section}>
              <td className={sectionstyles.cbse_checkbox}>
                <input type="checkbox" id={sectionstyles.test2} />
                <label for="test2"></label>
              </td>

              <td className={sectionstyles.cbse_table_head}> Grades</td>
              <td className={sectionstyles.cbse_table_head1}>Section Name</td>
              <td className={sectionstyles.cbse_table_head2}>Active</td>
              <td className={sectionstyles.cbse_table_head3}>Action</td>
            </tr>
          </thead>
          <tbody>
            {/* Start - render non mapped Grade rows */}
            {console.log(grades, "5555")}
            {grades.length > 0 &&
              grades.map((u) => {
                const myData = grades.data;
                const gradeId = u.id;
                const gradeName = u.grade;
                // if grade mapping already exists then do not render
                // if (!existingGrades.indexOf(gradeId))
                return (
                  <>
                    <tr className={sectionstyles.table_row_section}>
                      <td className={sectionstyles.cbse_checkbox}>
                        <input type="checkbox" id={sectionstyles.test2} />
                        <label for="test2"></label>
                      </td>
                      <td className={sectionstyles.cbse_table_head}>
                        {gradeName}
                      </td>
                      <td>
                        {console.log("---------showModal", showModal)}
                        {showModal ? (
                          <input
                            type="text"
                            value={newSetion}
                            onChange={(event) =>
                              setnewSection(event.target.value)
                            }
                          />
                        ) : (
                          <select
                            className={sectionstyles.table_data_section}
                            onChange={handleNewSection}
                          >
                            <option selected="selected" value={""}></option>
                            <option value={"ADD_NEW"}>New Section</option>

                            {sections.length !== 0 &&
                              sections.map((section) => {
                                return (
                                  <option value={section.id}>
                                    section {section.section}
                                  </option>
                                );
                              })}
                          </select>
                        )}
                      </td>
                      <td className={sectionstyles.cbse_table_head2}>
                        <label className={sectionstyles.active_switch}>
                          <input
                            type="checkbox"
                            value={toggle}
                            onClick={() => {
                              setToggle(!toggle);
                            }}
                          />
                          <span className={sectionstyles.active_slider}></span>
                        </label>
                      </td>
                      <td
                        className={sectionstyles.cbse_table_head3}
                        id={gradeId}
                      >
                        <button
                          className={sectionstyles.button}
                          onClick={async (e) => {
                            if (showModal) {
                              //console.log("in ifff--------->");
                              await addSection();
                            }
                            //console.log("out ifff--------->", tempNewSection);
                            props.addMappingNew(e, tempNewSection);
                          }}
                          id="section_table_btn"
                        >
                          Addx
                        </button>
                      </td>
                    </tr>
                  </>
                );
              })}
            {/* End - render non mapped Grade rows */}
            {console.log(gradeSection, "eeeeeeeee")}
          </tbody>
        </table>
      </div>
    </>
  );
};

const BranchSection = () => {
  const [addSubAndTopicVisible, setAddSubAndTopicVisible] = useState(true);
  const [isEdit, setIsEdit] = useState(false);
  const [gradesection, setGradesection] = useState({});
  const [updatedData, setUpdatedData] = useState();
  const [filterValue, setFilterValue] = useState();
  const [pageSize, setPageSize] = useState(100);
  const [pbsection, setPbsection] = useState({});
  const [showModal, setShowModal] = useState(false);

  //fetch current schoolId and branchId from session
  const schoolid = sessionStorage.getItem("schoolid");
  const branchid = sessionStorage.getItem("branchid");

  const [boardId, setBoardId] = useState("");
  const [boardName, setBoardName] = useState("");

  const [active, setActive] = useState("FirstTable");
  const navigate = useNavigate();

  //Start - Section Data
  const [haveSectionData, setHaveSectionData] = useState([]);
  const [selectedSectionData, setSelectedSectionData] = useState([
    false,
    false,
    true,
  ]);

  // 23/09 start
  //BranchController starts
  const [branchList, setBranchList] = useState([]);

  const [schoolDetails, setSchoolDetails] = useState({
    branch: sessionStorage.getItem("branchid"),
  });

  const handlerSchoolDetailsInputs = (e) => {
    setSchoolDetails({
      ...schoolDetails,
      [e.target.name]: e.target.value,
      // schoolName: e.target.id,
    });
  };

  useEffect(() => {
    var branchid = schoolDetails.branch;
    sessionStorage.setItem("branchid", branchid);
    Branches_Changer_School_Id(setBranchList);
    return () => {};
  }, [schoolDetails]);

  //BranchController ends

  // 23/09 end

  const handleSectionData = (e) => {
    const updatedSelectedSectionData = selectedSectionData.map(
      (selectedSection, index) => {
        if (index === parseInt(e.target.id)) {
          return true;
        } else {
          return false;
        }
      }
    );
    setSelectedSectionData(updatedSelectedSectionData);
  };
  //End - Section Data
 
  //Start - grade Section mapping
  const [grades, setGrades] = useState([]);
  const [sections, setSections] = useState([]);
  const [gradeSectionMapping, setGradeSectionMapping] = useState([]);
  const [newGradeSectionMapping, setNewGradeSectionMapping] = useState([]);
  const [newSection, setNewSection] = useState([]);
  let board_name = sessionStorage.getItem("boardName");
  const addGradeRow = (e) => {
    const [gradeId, sectionId] =
      e.target.parentNode.parentNode.parentNode.parentNode.parentNode.id.split(
        "_"
      );
    const updated = [...newGradeSectionMapping];

    //only push gradeId into array if it does not exist
    if (updated.indexOf(gradeId) === -1) {
      updated.push(gradeId);
    }
    setNewGradeSectionMapping(updated);
  };

  // get section by plan and branch
  const getSectionBranchPlan = async (setGrades) => {
    let data = await axios.get(
      `${
        process.env.REACT_APP_SWAGGER_URL
      }v1/api/master/grades/for-grade-section-mapping?planId=${sessionStorage.getItem(
        "planid"
      )}&boardId=${sessionStorage.getItem("boardid")}`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${sessionStorage.getItem("token")}`,
        },
      }
    );
    if (data.data.data) {
      const totalElement = data?.data?.totalElements;
      if (totalElement >= pageSize) {
        setPageSize(totalElement);
      }

      setGrades(data?.data?.data);
    }
  };
  // get section by plan and branch
  const addMapping = () => {
    const payload = {
      active: true,
      branchId: branchid,
      diffGradeSection: [
        {
          gradeId: newGradeSectionMapping[0],
          sectionId: newSection,
        },
      ],
      schoolId: schoolid,
      sectionData: "DIFFERENT_SECTIONS",
    };

    // axios.post(`${process.env.REACT_APP_SWAGGER_URL}v1/api/user/grade-section`,payload,{
    //   headers: {
    //     "Content-Type": "application/json",
    //     Authorization: `Bearer ${token}`,
    //   },
    // })
    // .then((resp)=>{
    //   console.log(resp)
    // })
    // .catch(err=>console.log(err))

    // createGradeSectionMapping(payload);
    // console.log("--------Clearr -- add mapping ");
    setNewSection([]);
    setNewGradeSectionMapping([]);
    gradeSectionGet(setGradeSectionMapping);
    // console.log("triggered");
    fetchData(0);
  };
  // console.log("OUT 1111111111", newSection);

  const addMappingNew = (e, newSection) => {
    // console.log("IN 1111111111", newSection);
    const payload = {
      active: true,
      branchId: branchid,
      diffGradeSection: [
        {
          gradeId: e.target.parentNode.id,

          sectionId: newSection,
        },
      ],
      schoolId: schoolid,
      sectionData: "DIFFERENT_SECTIONS",
    };
    createGradeSectionMapping(payload);
    // console.log("----clraer ---- add new mapping--");
    // setNewSection([]);
    gradeSectionGet(setGradeSectionMapping);
  };

  // Start - update grade -section mapping
  const [updateGrade, setUpdateGrade] = useState("");
  const handleUpdate = (e) => {
    fetchData(0);
    setUpdateGrade(e.target.parentNode.id);
  };
  const updateMapping = (e) => {
    const payload = {
      active: true,
      branchId: branchid,
      gradeId: updateGrade,
      schoolId: schoolid,
      sectionData: "DIFFERENT_SECTIONS",
      sectionId: newSection,
    };
    gradeSectionMappingUpdate(payload);
    setUpdateGrade("");
    // console.log("------Clear Update mapping ---->");
    setNewSection([]);
    gradeSectionGet(setGradeSectionMapping);
    fetchData(0);
  };
  // End - update grade -section mapping

  // Start - for toggling a grade-section mapping

  const handleDelete = (res) => {
    let data = {
      gradeId: res.gradeId,
      branchId: sessionStorage.getItem("branchid"),
      schoolId: sessionStorage.getItem("schoolid"),
      // "active": true
    };
    axios
      .delete(
        `${process.env.REACT_APP_SWAGGER_URL}v1/api/user/grade-section`,
        data,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${sessionStorage.getItem("token")}`,
          },
        }
      )
      .then((res) => console.log(res))
      .catch((err) => console.log(err));
  };

  const handleToggle = (e, res) => {
    // gradeSectionMappingToggle(e.target.id, !e.target.checked);
    let data = {
      gradeId: res.gradeId,
      branchId: sessionStorage.getItem("branchid"),
      schoolId: sessionStorage.getItem("schoolid"),
      active: !e.target.checked,
    };
    axios
      .put(
        `${process.env.REACT_APP_SWAGGER_URL}v1/api/user/grade-section/toggle/active`,
        data,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${sessionStorage.getItem("token")}`,
          },
        }
      )
      .then((res) => console.log(res))
      .catch((err) => console.log(err));
    gradeSectionGet(setGradeSectionMapping);
  };

  //end - for toggling a grade-section mapping
  const fetchData = async (pageSize) => {
    const response = await axios.get(
      `${
        process.env.REACT_APP_SWAGGER_URL
      }v1/api/user/grade-section?schoolId=${sessionStorage.getItem(
        "schoolid"
      )}&branchId=${sessionStorage.getItem("branchid")}`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${sessionStorage.getItem("token")}`,
        },
      }
    );
    if (response?.data?.data) {
      const totalElement = response?.data?.totalElements;
      if (totalElement >= pageSize) {
        setPageSize(totalElement);
      }

      setUpdatedData(response?.data?.data);
    }
  };

  //End - grade Section mapping

  const handleRemove = (e) => {
    setShowModal(true);
  };
  const handleRemoveNotNow = (e) => {
    setShowModal(false);
  };
  const handleMove = (e) => {
    setShowModal(false);
    navigate("/dashboard/CSP");
  };
  useEffect(() => {
    if (pageSize) {
      fetchData(pageSize);
    }
  }, [pageSize]);

  useEffect(() => {
    setFilterValue(updatedData);
  }, [updatedData]);

  useEffect(() => {
    BranchControllerBoardIdGet(setBoardId);
    BoardNameGet(boardId, setBoardName);

    sectionDataGet(setHaveSectionData);
    // gradeListGet(setGrades);
    gradeSectionGet(setGradeSectionMapping);
    sectionListGet(setSections);
    getSectionBranchPlan(setGrades);

    return () => {};
  }, []);

  return (
    <>
      <article>
        <div className={dashboardContentStyles.dashboard_link}>
          <span className={dashboardContentStyles.link_icon}>
            <img
              src={dashHome}
              alt="no img"
              width="15px"
              height="20px"
              style={{ verticalAlign: "middle" }}
            />{" "}
          </span>
          {"  "}
          <span className={dashboardContentStyles.link_text}>Home</span>{" "}
          <span>
            <i className="fa-solid fa-angle-right"></i>
          </span>
          <span>
            <i className="fa-solid fa-angle-right"></i>
          </span>
          {"  "}
          <a>Section Details</a>
        </div>
      </article>
      <article className={sectionstyles.article_add_school_plan}>
        <div className={sectionstyles.Grid_school_name_branch}>
          <div className={sectionstyles.school_name}>
            <School_name />
          </div>
          <select
            className={sectionstyles.branchselect_Dropdown}
            name="branch"
            id="branch"
            value={schoolDetails.branch}
            onChange={handlerSchoolDetailsInputs}
          >
            {branchList.length > 0 ? (
              branchList.map((branchList) => {
                return (
                  <option value={branchList.branchId}>
                    {branchList.branch}
                  </option>
                );
              })
            ) : (
              <option className={sectionstyles.option} value=""></option>
            )}
          </select>
          {/* <button className={sectionstyles.btn_branch_name}>
            AZVASA Mumbai Branch <i className="fa-solid fa-chevron-down"></i>
          </button> */}
        </div>
        <div className={sectionstyles.grid_container_header_plan}>
          <div
            className={sectionstyles.item_plan1}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/branchplan");
            }}
          >
            Plan
          </div>
          <div
            className={sectionstyles.item_plan2}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/branchsection");
            }}
          >
            Sections
          </div>
          <div
            className={sectionstyles.item_plan3}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/teacherId", {
                state: "edit",
              });
            }}
          >
            Academic Staff
          </div>
          <div
            className={sectionstyles.item_plan4}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/student");
            }}
          >
            Students
          </div>
          <div
            className={sectionstyles.item_plan5}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/csp");
            }}
          >
            Change Student Profile
          </div>
          <div
            className={sectionstyles.item_plan6}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/token");
            }}
          >
            Tokens
          </div>
          <div
            className={sectionstyles.item_plan7}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/logo");
            }}
          >
            Branch Logo
          </div>
        </div>

        <div className={sectionstyles.boards_container}>
          <div className={sectionstyles.grid_item}>
            <label id="boards">Board</label>
            {board_name}
          </div>
          {/* Start - made the radio inputs dynamic */}
          {haveSectionData.length === 0 ? (
            <p>No Section Data Available</p>
          ) : (
            haveSectionData.map((sectionData, index) => {
              return (
                <div
                  className={sectionstyles.radio}
                  key={sectionData.sectionData}
                >
                  <input
                    id={index}
                    type="checkbox"
                    checked={selectedSectionData[index]}
                    onClick={handleSectionData}
                  />
                  <label for="test2">{sectionData.sectionName}</label>
                </div>
              );
            })
          )}
        </div>
        {/* End - made the radio inputs dynamic */}
        {/* Start - section Table */}
        <GradeSectionMapping
          grades={grades}
          gradeSection={gradeSectionMapping}
          addGradeRow={addGradeRow}
          newGradeSectionMapping={newGradeSectionMapping}
          newSection={newSection}
          setNewSection={setNewSection}
          sections={sections}
          addMapping={addMapping}
          updateGrade={updateGrade}
          handleUpdate={handleUpdate}
          updateMapping={updateMapping}
          handleToggle={handleToggle}
          addMappingNew={addMappingNew}
        />
        <div style={{ marginTop: "1540px" }}>
          <div className={sectionstyles.section_list_table}>
            <table className={sectionstyles.table_section}>
              <tbody>
                {filterValue && filterValue.length > 0
                  ? filterValue?.map((res) => {
                      return (
                        <tr
                        //className={sectionstyles.table_row_section}
                        // id={`${gradeId}_${grade_section_map.sectionId}`}
                        >
                          <td
                            className={sectionstyles.cbse_checkbox}
                            style={{ padding: "0px" }}
                          >
                            <input type="checkbox" id={sectionstyles.test2} />
                            <label for="test2"></label>
                          </td>
                          <td
                            className={sectionstyles.cbse_table_head}
                            style={{ padding: "0px" }}
                          >
                            {res.gradeName}
                          </td>
                          <td
                            className={sectionstyles.cbse_table_head}
                            style={{ padding: "0px" }}
                          >
                            <div
                              style={{
                                display: "flex",
                                color: "black",
                                flexDirection: "column",
                              }}
                            >
                              {res.gradeSectionMapping &&
                              res.gradeSectionMapping.length > 0 ? (
                                res.gradeSectionMapping.map((data, i) => (
                                  <p style={{ color: "black" }}>
                                    {data.sectionName}
                                  </p>
                                ))
                              ) : (
                                <p>-</p>
                              )}
                            </div>

                            {/* {grade_section_map.sectionName} */}
                          </td>
                          <td
                            className={sectionstyles.cbse_table_head2}
                            style={{ padding: "0px" }}
                          >
                            <label className={sectionstyles.active_switch}>
                              <input
                                // id={grade_section_map.id}
                                type="checkbox"
                                // checked={!grade_section_map.active}
                                onClick={(e) => handleToggle(e, res)}
                              />
                              <span
                                className={sectionstyles.active_slider}
                              ></span>
                            </label>{" "}
                          </td>
                          <td className={sectionstyles.cbse_table_head3}>
                            <div className={sectionstyles.wrapper}>
                              <div className={sectionstyles.icon}>
                                <div className={sectionstyles.tooltip}>
                                  Add section
                                </div>
                                <span
                                // onClick={addGradeRow}
                                >
                                  <i
                                    className="fa-regular fa-circle-plus"
                                    // onClick={props.onClickHandler}
                                    id="section_add_more_btn"
                                  ></i>
                                </span>
                              </div>
                              <div className={sectionstyles.icon}>
                                <div className={sectionstyles.tooltip}>
                                  Edit
                                </div>
                                <span
                                // id={gradeId}
                                >
                                  <i
                                    className="fa-solid fa-pencil"
                                    // onClick={handleUpdate}
                                  ></i>
                                </span>
                              </div>
                              <div className={sectionstyles.icon}>
                                <div className={sectionstyles.tooltip}>
                                  Remove
                                </div>
                                <span
                                  // id={grade_section_map.id}
                                  onClick={(res) => {
                                    handleDelete(res);
                                  }}
                                >
                                  <i className="fa-solid fa-trash-can"></i>
                                </span>
                              </div>
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  : ""}
              </tbody>
            </table>
          </div>
        </div>

        {/* End - section Table */}
        {/* modal Begin */}
        {/* <div
          className={removestyle.flex_container}
          id={removestyle.modal}
          style={showModal ? { display: "flex" } : { display: "none" }}
        >
          <div
            className={removestyle.main_content}
            id={removestyle.modal_content}
          >
            <span>
              <img src={ErrorImage} alt="red checkmark" />
            </span>
            <h5>You cannot delete the grade</h5>
            <p>First, you move Student</p>

            <div className={removestyle.modal_buttons}>
              <button
                className={removestyle.choosefile_button_1}
                id={removestyle.choose_button_id_1}
                onClick={handleRemoveNotNow}
              >
                NOT NOW
              </button>
              <button
                className={removestyle.choosefile_button_2}
                id={removestyle.choose_button_id_2}
                onClick={handleMove}
              >
                MOVE
              </button>
            </div>
          </div>
        </div> */}
        {/* modal End */}
      </article>
    </>
  );
};
export default BranchSection;
