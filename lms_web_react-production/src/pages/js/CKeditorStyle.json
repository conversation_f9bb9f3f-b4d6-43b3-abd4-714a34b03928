{"ckEditorStyles": "h3.category {\n  font-family: '<PERSON><PERSON> Neue';\n  font-size: 20px;\n  font-weight: bold;\n  color: red;\n  letter-spacing: 10px;\n  margin: 0;\n  padding: 0;\n}\n\n h2.document-title {\n  font-family: '<PERSON><PERSON> Neue';\n  font-size: 50px;\n  font-weight: bold;\n  margin: 0;\n  padding: 0;\n  border: 0;\n}\n\n  p.info-box {\n  --background-size: 30px;\n  --background-color: #e91e63;\n  padding: 1.2em 2em;\n  border: 1px solid var(--background-color);\n  background: linear-gradient(135deg, var(--background-color) 0%, var(--background-color) var(--background-size), transparent var(--background-size)), linear-gradient(135deg, transparent calc(100% - var(--background-size)), var(--background-color) calc(100% - var(--background-size)), var(--background-color));\n  border-radius: 10px;\n  margin: 1.5em 2em;\n  box-shadow: 5px 5px 0 #ffe6ef;\n}\n\n  blockquote.side-quote {\n  font-family: '<PERSON><PERSON> Neue';\n  font-style: normal;\n  float: right;\n  width: 35%;\n  position: relative;\n  border: 0;\n  overflow: visible;\n  z-index: 1;\n  margin-left: 1em;\n}\n\n  span.marker {\n  background: yellow;\n}\n\n  span.spoiler {\n  background: #000;\n  color: #000;\n}\n\n  span.spoiler:hover {\n  background: #000;\n  color: #fff;\n}\n  .columns-2 { \n column-count: 2;\n  column-gap: 100px; \n  text-align: justify !important;\n padding: 109px !important;\n}\n   .columns-3 { \n column-count: 3;\n  column-gap: 60px; \n  text-align: justify !important;\n padding: 109px !important;\n}", "styles": [{"name": "style1", "style": "h1, h2, h3, h4, h5, h6, p { font-family: 'Roboto', sans-serif; color: #000; }\n\nhr { border: 1px solid black; }\n\ntable { border-collapse: collapse; }\n\nthead { background-color: rgb(222, 221, 221); }\n\ntd, th { border: 2px solid #000; }\n\npre { background-color: lightgrey; border-radius: 5px; padding: 10px; }\n\n.todo-list li { display: flex; align-items: center; }\n\n.marker-yellow { background-color: yellow; }\n\n.marker-pink { background-color: pink; }\n\n.marker-blue { background-color: blue; }\n\n.marker-green { background-color: lightgreen; }\n\n.pen-red { color: red; background: none; }\n\n.pen-green { color: green; background: none; }"}, {"name": "style2", "style": "h1,\n        h2,\n        h3,\n        h4,\n        h5,\n        h6,\n        p {\n            color: green;\n     font-style: italic;\n       background-color: rgb(200, 255, 0);\n        }\n\n        p {\n            line-height: 1.6;\n            margin: 10px 0;\n        }\n\n        /* Styling ordered lists */\n        ol {\n            margin-left: 20px;\n            padding-left: 20px;\n        }\n\n        /* Styling unordered lists */\n        ul {\n            margin-left: 20px;\n            padding-left: 20px;\n        }\n\n        /* Styling list items */\n        li {\n            margin-bottom: 5px;\n        }\n\n        /* Ensuring justified text in paragraphs */\n        p,\n        li {\n            text-align: justify;\n        }\n\n        /* Additional optional styling, can be adjusted */\n        body {\n            font-family: \"Open Sans\", Arial, sans-serif !important;\n            font-size: 14px;\n            color: rgb(0, 0, 0);\n        }\n\n        img {\n            width: 25%;\n            height: 25%;\n        }\n"}], "templates": [{"name": "Two Side by Side Text", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <style>\n         .columns-2 {\n  column-count: 2;  \n  column-gap: 100px; /* Adjust gap between columns */\n  text-align: justify !important;\n  padding: 109px !important;\n}\n \n.columns-3 {\n  column-count: 3;  \n  column-gap: 60px; /* Adjust gap between columns */\n  text-align: justify !important;\n  padding: 109px !important;\n}\n    </style>\n</head>\n<body>\n    <p class='columns-2' style='-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(32, 33, 34);font-family:sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;margin-bottom:1em;margin-right:0px;margin-top:0.5em;orphans:2;text-align:justify;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;'>In the late Vedic period, around the 6th century BCE, the small states and chiefdoms of the Ganges Plain and the north-western regions had consolidated into 16 major oligarchies and monarchies that were known as the mahajanapadas.[93][94] The emerging urbanisation gave rise to non-Vedic religious movements, two of which became independent religions. Jainism came into prominence during the life of its exemplar, Mahavira.[95] Buddhism, based on the teachings of Gautama Buddha, attracted followers from all social classes excepting the middle class; chronicling the life of the Buddha was central to the beginnings of recorded history in India.[96][97][98] In an age of increasing urban wealth, both religions held up renunciation as an ideal,[99] and both established long-lasting monastic traditions. Politically, by the 3rd century BCE, the kingdom of Magadha had annexed or reduced other states to emerge as the Mauryan Empire.[100] The empire was once thought to have controlled most of the subcontinent except the far south, but its core regions are now thought to have been separated by large autonomous areas.[101][102] The Mauryan kings are known as much for their empire-building and determined management of public life as for Ashoka's renunciation of militarism and far-flung advocacy of the Buddhist dhamma.[103][104] The Sangam literature of the Tamil language reveals that, between 200 BCE and 200 CE, the southern peninsula was ruled by the Cheras, the Cholas, and the Pandyas, dynasties that traded extensively with the Roman Empire and with West and Southeast Asia.[105][106] In North India, Hinduism asserted patriarchal control within the family, leading to increased subordination of women.[107][100] By the 4th and 5th centuries, the Gupta Empire had created a complex system of administration and taxation in the greater Ganges Plain; this system became a model for later Indian kingdoms.[108][109] Under the Guptas, a renewed Hinduism based on devotion, rather than the management of ritual, began to assert itself.[110] This renewal was reflected in a flowering of sculpture and architecture, which found patrons among an urban elite.[109] Classical Sanskrit literature flowered as well, and Indian science, astronomy, medicine, and mathematics made significant advances.</p>\n</body>\n</html>"}, {"name": "Three Side by Side Text", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <style>\n         .columns-2 {\n  column-count: 2;  \n  column-gap: 100px; /* Adjust gap between columns */\n  text-align: justify !important;\n  padding: 109px !important;\n}\n \n.columns-3 {\n  column-count: 3;  \n  column-gap: 60px; /* Adjust gap between columns */\n  text-align: justify !important;\n  padding: 109px !important;\n}\n    </style>\n</head>\n<body>\n    <p class='columns-3' style='-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(32, 33, 34);font-family:sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;margin-bottom:1em;margin-right:0px;margin-top:0.5em;orphans:2;text-align:justify;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;'>In the late Vedic period, around the 6th century BCE, the small states and chiefdoms of the Ganges Plain and the north-western regions had consolidated into 16 major oligarchies and monarchies that were known as the mahajanapadas.[93][94] The emerging urbanisation gave rise to non-Vedic religious movements, two of which became independent religions. Jainism came into prominence during the life of its exemplar, Mahavira.[95] Buddhism, based on the teachings of Gautama Buddha, attracted followers from all social classes excepting the middle class; chronicling the life of the Buddha was central to the beginnings of recorded history in India.[96][97][98] In an age of increasing urban wealth, both religions held up renunciation as an ideal,[99] and both established long-lasting monastic traditions. Politically, by the 3rd century BCE, the kingdom of Magadha had annexed or reduced other states to emerge as the Mauryan Empire.[100] The empire was once thought to have controlled most of the subcontinent except the far south, but its core regions are now thought to have been separated by large autonomous areas.[101][102] The Mauryan kings are known as much for their empire-building and determined management of public life as for Ashoka's renunciation of militarism and far-flung advocacy of the Buddhist dhamma.[103][104] The Sangam literature of the Tamil language reveals that, between 200 BCE and 200 CE, the southern peninsula was ruled by the Cheras, the Cholas, and the Pandyas, dynasties that traded extensively with the Roman Empire and with West and Southeast Asia.[105][106] In North India, Hinduism asserted patriarchal control within the family, leading to increased subordination of women.[107][100] By the 4th and 5th centuries, the Gupta Empire had created a complex system of administration and taxation in the greater Ganges Plain; this system became a model for later Indian kingdoms.[108][109] Under the Guptas, a renewed Hinduism based on devotion, rather than the management of ritual, began to assert itself.[110] This renewal was reflected in a flowering of sculpture and architecture, which found patrons among an urban elite.[109] Classical Sanskrit literature flowered as well, and Indian science, astronomy, medicine, and mathematics made significant advances.</p>\n</body>\n</html>"}, {"name": "Template 1", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n <style>\n        .scienceTemplate2 {\n            font-family: 'Trebuchet MS', sans-serif;\n            background-color: #fafafa;\n            color: #444;\n            padding: 30px;\n        }\n\n        .scienceTemplate2 h1,\n        .scienceTemplate2 h2,\n        .scienceTemplate2 h3 {\n            font-family: 'Helvetica', sans-serif;\n            color: #d84315;\n        }\n\n        .scienceTemplate2 h1 {\n            font-size: 2.2em;\n            text-align: center;\n            margin-bottom: 30px;\n            text-transform: uppercase;\n        }\n\n        .scienceTemplate2container2 {\n            background-color: #fff;\n            border-radius: 12px;\n            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n            padding: 25px;\n        }\n\n        .sciencTemplate2container {\n            display: flex;\n            flex-wrap: wrap;\n        }\n\n        .scienceTemplate2section {\n            width: 100%;\n            margin: 20px 0;\n        }\n\n        .scienceTemplate2section-title {\n            font-size: 1.8em;\n            color: #ff5722;\n            margin-bottom: 15px;\n            border-bottom: 2px solid #ff5722;\n            padding-bottom: 8px;\n            text-transform: uppercase;\n        }\n\n        .scienceTemplate2content-section {\n            display: flex;\n            align-items: flex-start;\n            justify-content: space-between;\n        }\n\n        .scienceTemplate2text-content {\n            width: 48%;\n            padding: 15px;\n            border-radius: 8px;\n        }\n\n        .sectionTemplate2image-content {\n            width: 48%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }\n\n        .sectionTemplate2image-content img {\n            max-width: 100%;\n            height: auto;\n            border-radius: 8px;\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n        }\n\n        .scienceTemplate2footer {\n            display: flex;\n            justify-content: center;\n            background-color: #ffccbc;\n            text-align: center;\n            padding: 10px;\n            font-size: 1em;\n            margin-top: 30px;\n            border-radius: 10px;\n            color: #333;\n        }\n    </style>\n</head>\n\n<body>\n    <div class=\"scienceTemplate2\">\n        <div class=\"scienceTemplate2container2\">\n            <div class=\"sciencTemplate2container\">\n                <h1>Science Content - The Water Cycle</h1>\n                <div class=\"scienceTemplate2section\">\n                    <div class=\"scienceTemplate2section-title\">Introduction</div>\n                    <div class=\"scienceTemplate2content-section\">\n                        <div class=\"scienceTemplate2text-content\">\n                            The water cycle, also known as the hydrological cycle, describes the continuous movement of water\n                            on, above, and below the surface of the Earth. This process involves various stages such as\n                            evaporation, condensation, precipitation, and collection.\n                        </div>\n                        <div class=\"sectionTemplate2image-content\">\n                            <img src=\"https://picsum.photos/300/200\" alt=\"Water Cycle Diagram\">\n                        </div>\n                    </div>\n                </div>\n\n                <div class=\"scienceTemplate2section\">\n                    <div class=\"scienceTemplate2section-title\">Evaporation</div>\n                    <div class=\"scienceTemplate2content-section\">\n                        <div class=\"sectionTemplate2image-content\">\n                            <img src=\"https://picsum.photos/300/200\" alt=\"Evaporation Process\">\n                        </div>\n                        <div class=\"scienceTemplate2text-content\">\n                            Evaporation occurs when water is heated by the sun and turns into water vapor. This is a crucial\n                            first step in the water cycle and contributes to the formation of clouds as water vapor rises\n                            into the atmosphere.\n                        </div>\n                    </div>\n                </div>\n\n                <div class=\"scienceTemplate2section\">\n                    <div class=\"scienceTemplate2section-title\">Condensation</div>\n                    <div class=\"scienceTemplate2content-section\">\n                        <div class=\"scienceTemplate2text-content\">\n                            Condensation is the process by which water vapor cools and changes back into liquid droplets,\n                            forming clouds. This step is important as it leads to precipitation. Condensation is the process\n                            by which water vapor cools and changes back into liquid droplets, forming clouds. This step is\n                            important as it leads to precipitation. Condensation is the process by which water vapor cools\n                            and changes back into liquid droplets, forming clouds. This step is important as it leads to\n                            precipitation. Condensation is the process by which water vapor cools and changes back into\n                            liquid droplets, forming clouds. This step is important as it leads to precipitation.\n                        </div>\n                        <div class=\"sectionTemplate2image-content\">\n                            <img src=\"https://picsum.photos/300/200\" alt=\"Condensation in Clouds\">\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div class=\"scienceTemplate2footer\">\n                © 2024 Azvasa Enriching Education. All Rights Reserved.\n            </div>\n        </div>\n    </div>\n</body>\n\n</html>"}, {"name": "Template2", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n <style>\n        .scienceTemplate {\n            font-family: 'Georgia', serif;\n            margin: 20px;\n            padding: 20px;\n            background-color: #e8f0fe;\n            color: #333;\n        }\n\n        .scienceTemplate h1,\n        .scienceTemplate h2,\n        .scienceTemplate h3 {\n            color: #1e88e5;\n        }\n\n        .scienceTemplate h1 {\n            font-size: 2.5em;\n            text-align: center;\n            margin-bottom: 20px;\n        }\n\n        .scienceTemplateContainer {\n            background: #fff;\n            border-radius: 12px;\n            padding: 25px;\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n        }\n\n        .scienceTemplateSection {\n            margin: 30px 0;\n        }\n\n        .scienceTemplatesection-title {\n            font-size: 1.8em;\n            margin-bottom: 15px;\n            border-bottom: 2px solid #1e88e5;\n            padding-bottom: 10px;\n        }\n\n        .scienceTemplateimage-section {\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            margin-bottom: 20px;\n        }\n\n        .scienceTemplateimage-section img {\n            max-width: 100%;\n            height: auto;\n            border-radius: 10px;\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n        }\n\n        .scienceTemplatecontent {\n            padding: 15px;\n            background-color: #f9f9f9;\n            border-left: 5px solid #1e88e5;\n            border-radius: 8px;\n            text-align: justify;\n        }\n\n        .scienceTemplatefooter {\n            background-color: #bbdefb;\n            padding: 15px;\n            text-align: center;\n            margin-top: 30px;\n            border-radius: 8px;\n            font-size: 0.9em;\n            color: #555;\n        }\n    </style>\n</head>\n\n<body>\n    <div class=\"scienceTemplate\">\n        <div class=\"scienceTemplateContainer\">\n            <h1>Science Content</h1>\n\n            <div class=\"scienceTemplateSection\">\n                <div class=\"scienceTemplatesection-title\">Subject: <strong>Science</strong></div>\n                <div class=\"scienceTemplatecontent\">Topic: <strong>The Solar System</strong></div>\n            </div>\n\n            <div class=\"scienceTemplateSection\">\n                <div class=\"scienceTemplatesection-title\">Introduction</div>\n                <div class=\"scienceTemplatecontent\">\n                    The solar system consists of the Sun and everything that orbits it, including the planets, moons,\n                    asteroids, and comets.\n                    This lesson will explore the components of the solar system and how they interact with one another.\n                </div>\n            </div>\n\n            <div class=\"scienceTemplateSection\">\n                <div class=\"scienceTemplatesection-title\">Image Section</div>\n                <div class=\"scienceTemplateimage-section\">\n                    <img src=\"https://picsum.photos/200/300\" alt=\"The Solar System\">\n                </div>\n            </div>\n\n            <div class=\"scienceTemplateSection\">\n                <div class=\"scienceTemplatesection-title\">Main Content</div>\n                <div class=\"scienceTemplatecontent\">\n                    Our solar system consists of eight planets that revolve around the Sun. The four inner planets—Mercury,\n                    Venus, Earth, and Mars—are rocky planets, while the outer planets—Jupiter, Saturn, Uranus, and\n                    Neptune—are gas giants.\n                    Our solar system consists of eight planets that revolve around the Sun. The four inner\n                    planets—Mercury,\n                    Venus, Earth, and Mars—are rocky planets, while the outer planets—Jupiter, Saturn, Uranus, and\n                    Neptune—are gas giants. Our solar system consists of eight planets that revolve around the Sun. The four\n                    inner planets—Mercury, Venus, Earth, and Mars—are rocky planets, while the outer planets—Jupiter,\n                    Saturn, Uranus, and Neptune—are gas giants. Our solar system consists of eight planets that revolve\n                    around the Sun. The four inner planets—Mercury, Venus, Earth, and Mars—are rocky planets, while the outer\n                    planets—Jupiter, Saturn, Uranus, and Neptune—are gas giants. Our solar system consists of eight planets\n                    that revolve around the Sun. The four inner planets—Mercury, Venus, Earth, and Mars—are rocky planets,\n                    while the outer planets—Jupiter, Saturn, Uranus, and Neptune—are gas giants. Our solar system consists\n                    of eight planets that revolve around the Sun. The four inner planets—Mercury, Venus, Earth, and\n                    Mars—are rocky planets, while the outer planets—Jupiter, Saturn, Uranus, and Neptune—are gas giants.\n                </div>\n            </div>\n\n            <div class=\"scienceTemplatefooter\">\n                © 2024 Azvasa Enriching Education. All Rights Reserved.\n            </div>\n        </div>\n</body>\n\n</html>"}, {"name": "Template 3", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n <style>\n        .socialTemplate {\n            font-family: Arial, sans-serif;\n            margin: 20px;\n            padding: 20px;\n            background-color: #f4f4f4;\n            color: #333;\n        }\n\n        .socialTemplate h1,\n        .socialTemplate h2,\n        .socialTemplate h3 {\n            color: #4CAF50;\n        }\n\n        .socialTemplate h1 {\n            border-bottom: 2px solid #4CAF50;\n            padding-bottom: 10px;\n        }\n\n        .socialTemplatecontainer {\n            background: #fff;\n            border-radius: 8px;\n            padding: 20px;\n            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n        }\n\n        .socialTemplatesection {\n            margin: 20px 0;\n        }\n\n        .socialTemplatesection-title {\n            font-size: 1.5em;\n            margin-bottom: 10px;\n            padding-bottom: 5px;\n            border-bottom: 1px solid #ccc;\n        }\n\n        .socialTemplatebook {\n            display: flex;\n            justify-content: center;\n            text-align: justify;\n        }\n\n        .socialTemplatecontent1 {\n            padding: 10px;\n            width: 50%;\n        }\n\n        .socialTemplatenote {\n            background-color: #fff8c6;\n            border-left: 5px solid #ffa500;\n            padding: 10px;\n            margin: 10px 0;\n        }\n\n        .socialTemplatecontent {\n            border: 1px solid #ccc;\n            padding: 10px;\n            margin-bottom: 10px;\n            background-color: #fafafa;\n        }\n    </style>\n</head>\n\n<body>\n    <div class=\"socialTemplate\">\n        <div class=\"socialTemplatecontainer\">\n            <h1>Content Heading</h1>\n\n            <div class=\"socialTemplatesection\">\n                <div class=\"socialTemplatesection-title\">Subject: <strong>Social Science</strong></div>\n                <div class=\"socialTemplatecontent\">Topic: <strong>The Impact of Globalization</strong></div>\n            </div>\n\n            <div class=\"socialTemplatesection\">\n                <div class=\"socialTemplatesection-title\">Objectives</div>\n                <div class=\"socialTemplatecontent\">\n                    - Understand the concept of globalization.<br>\n                    - Analyze the effects of globalization on local economies.<br>\n                    - Evaluate cultural exchanges in the context of globalization.\n                </div>\n            </div>\n\n            <div class=\"socialTemplatesection\">\n                <div class=\"socialTemplatesection-title\">Content</div>\n                <div class=\"socialTemplatebook\">\n                    <div class=\"socialTemplatecontent1\">\n                        Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.\n                        Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                    </div>\n                    <div class=\"socialTemplatecontent1\">\n                        Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.\n                        Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                        This lesson explores how globalization has transformed industries, influenced cultural practices, and created new opportunities and challenges worldwide.Globalization refers to the increasing interconnectedness of economies, cultures, and populations.\n                    </div>\n                </div>\n            </div>\n\n            <div class=\"socialTemplatesection\">\n                <div class=\"socialTemplatesection-title\">Footer</div>\n                <div class=\"socialTemplatecontent2\">\n                    - Group discussion on the advantages and disadvantages of globalization.<br>\n                    - Case study analysis of a local business affected by global trade.<br>\n                    - Research assignment on cultural impacts of globalization in various regions.\n                </div>\n            </div>\n\n            <footer style=\"margin-top: 30px; font-size: 0.9em; text-align: center; color: #777;\">\n                © 2024 Azvasa Enriching Education. All Rights Reserved.\n            </footer>\n        </div>\n    </div>\n</body>\n\n</html>"}]}