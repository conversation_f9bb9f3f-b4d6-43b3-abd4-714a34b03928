import { Box, Modal, Typography } from "@mui/material";
import ReportGmailerrorredOutlinedIcon from "@mui/icons-material/ReportGmailerrorredOutlined";
import YellowButton from "../../components/common/yellowButton";
import YellowBorderHover from "../../components/common/YellowBorderHover";
const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 500,
  bgcolor: "background.paper",
  // border: "2px solid #000",
  boxShadow: 24,
  borderRadius: 4,
  p: 4,
};
const ConfirmationModal = ({ open, close, confirmationmessage,updateToggle }) => {
  return (
    <Modal
      open={open}
      onClose={close}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box sx={style}>
        <Typography id="modal-modal-title" variant="h6" component="h2">
          <div style={{ display: "flex", justifyContent: "center" }}>
            <ReportGmailerrorredOutlinedIcon
              style={{ color: "#FFA700", fontSize: 120 }}
            />
          </div>
        </Typography>
        <Typography id="modal-modal-description" sx={{ mt: 2 }}>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              // flexDirection: "column",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                flexDirection: "column",
              }}
            >
              <p
                style={{
                  display: "flex",
                  justifyContent: "center",
                  color: "#FFA700",
                  padding: "4px",
                }}
              >
                {confirmationmessage}
              </p>
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  flexDirection: "row",
                  columnGap: "1rem",
                }}
              >
                {confirmationmessage ==
                "No Permission to delete or deactivate, as it has existing mapping in the workflow..!" ? (
                  <YellowButton onClick={close}>OK</YellowButton>
                ) : (
                  <>
                    <YellowBorderHover onClick={updateToggle}>Yes</YellowBorderHover>
                    <YellowButton onClick={close}>No</YellowButton>
                  </>
                )}
              </div>
            </div>
          </div>
        </Typography>
      </Box>
    </Modal>
  );
};
export default ConfirmationModal;
