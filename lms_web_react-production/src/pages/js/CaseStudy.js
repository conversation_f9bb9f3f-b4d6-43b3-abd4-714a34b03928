import React from 'react'
import QbStyles from './../css/questionbank.module.css'
import studentFileStyles from './../css/studentFiles.module.css'
import createschool from './../css/CreateSchool.module.css'
import {
    Paper,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    OutlinedInput
} from '@mui/material'

const CaseStudy = () => {
  return (
    <div>
        <Paper>
            <div>
                <div>
                <div>
                        <FormControl style={{ marginRight: '8px', marginLeft: '8px' }} sx={{ width: 300 }}>
                            <InputLabel className={QbStyles.select_input} id="demo-multiple-checkbox-label">
                                *Board
                            </InputLabel>
                            <Select
                                name="boardId"
                                labelId="demo-multiple-checkbox-label"
                                id="demo-multiple-checkbox"
                                // value={chapterInputs.boardId}
                                // onChange={handlerChapterInputs}
                                input={<OutlinedInput label="Tag" />}
                            // renderValue={(selected) => selected.join(", ")}
                            >
                                {/* {optionsRender(board)} */}
                                {/* {board.length > 0 ? (
                      board.map((board, i) => {
                        return (
                          <MenuItem key={i} value={board.id}>
                            <ListItemText primary={board.board} />
                          </MenuItem>
                        )
                      })
                    ) : <MenuItem>Select</MenuItem>} */}

                            </Select>
                        </FormControl>
                        <FormControl style={{ marginRight: '8px', marginLeft: '8px' }} sx={{ width: 300 }}>
                            <InputLabel className={QbStyles.select_input} id="demo-multiple-checkbox-label">
                                *Grade(s)
                            </InputLabel>
                            <Select
                                name="gradeId"
                                labelId="demo-multiple-checkbox-label"
                                id="demo-multiple-checkbox"
                                input={<OutlinedInput label="Tag" />}
                            // value={chapterInputs.gradeId}
                            // onChange={handlerChapterInputs}
                            >
                                {/* {gradesName.length > 0 ? gradesName.map((elem, i) => {
                      return (
                        <MenuItem key={i} value={elem.id}>
                          <ListItemText primary={elem.grade} />
                        </MenuItem>
                      )

                    }) : <MenuItem>Select</MenuItem>} */}
                                {/* {optionsRender(gradesName)} */}

                            </Select>
                        </FormControl>

                    </div>
                </div>
            </div>

            <div>
            <div>
                        <div>
                            <h3>Case Study</h3>
                            <p>Question</p>
                            <div style={{padding:'30px', boxShadow: 'rgb(0 0 0 / 16%) 0px 3px 6px' }}>
                                <div style={{ display: 'flex', border: '1px solid grey', padding: '5px', boxShadow: '0px 1px 1px #00000029' }}>
                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}>B</button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-italic"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-sliders"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-regular fa-image"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-arrow-right"></i></button>
                                    </div>

                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-right"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-center"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-left"></i></button>
                                    </div>

                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-eye"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-check"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-trash"></i></button>

                                    </div>



                                </div>
                                <textarea id="w3review" style={{ resize: 'none', width: '100%' }} name="w3review" rows="4" cols="50"></textarea>
                            </div>
                        </div>

                        <div>
                            <div style={{ display: 'flex' }}>
                                <div
                                    style={{ marginInline: "10px", display: "flex" }}
                                    className={studentFileStyles.radio_teacherid}
                                >
                                    <input
                                        type="radio"
                                        name="mapType"
                                        //   id={res.code}
                                        defaultChecked="checked"
                                        className={studentFileStyles.radio_active}
                                        placeholder=" "
                                    //   value={res.code}
                                    //   onClick={typeBtnHandler}
                                    // onChange={handleStudentInputs}
                                    // onChange={handlerTeacherInputs}
                                    />
                                    <label for="test_teacherid">Upload from my machine</label>
                                </div>

                                <div
                                    style={{ marginInline: "10px", display: "flex" }}
                                    className={studentFileStyles.radio_teacherid}
                                >
                                    <input
                                        type="radio"
                                        name="mapType"
                                        //   id={res.code}
                                        // defaultChecked="checked"
                                        className={studentFileStyles.radio_active}
                                        placeholder=" "
                                    //   value={res.code}
                                    //   onClick={typeBtnHandler}
                                    // onChange={handleStudentInputs}
                                    // onChange={handlerTeacherInputs}
                                    />
                                    <label for="test_teacherid">Use from Repository</label>
                                </div>
                            </div>
                            <div>
                                <div className={createschool.upload_logo_container}>
                                    <span>*Upload Files</span>
                                    <span className={createschool.custom_file_container}>
                                        <input
                                            type="file"
                                            name="contentUrl"
                                            style={{ width: "10rem" }}
                                            // id={sessionStorage.getItem("mapUrl")}
                                            // filename={sessionStorage.getItem("mapUrl")}
                                            // Value={mapInputs.mapUrl}
                                            // onError={setIsError(true)}
                                            // onChange={imageChange}
                                            className={createschool.custom_file_input}
                                        />

                                        {/* {mapInputs.mapUrl && (
                        <span>
                          <img
                            // src={mapInputs.mapUrl}
                            alt="Thumb"
                            width="75"
                            height="75"
                          />
                          <span>
                            <a>
                              {mapInputs.mapUrl.substring(
                                mapInputs.mapUrl.lastIndexOf("_") + 1,
                                mapInputs.mapUrl.length
                              )}
                            </a>
                            <br></br>
                            <br></br>
                            <a
                            //   onClick={removeSelectedImage}
                              style={{
                                color: "red",
                                textDecoration: "underline",
                              }}
                            >
                              Remove This Image
                            </a>
                          </span>
                        </span>
                      )} */}
                                    </span>
                                </div>
                            </div>

                        </div>
                    </div>
            </div>
            <div style={{display:'flex', justifyContent:'space-around'}}>
            <div>
                    <h6>Choice -A</h6>
                    <div style={{ boxShadow: 'rgb(0 0 0 / 16%) 0px 3px 6px' }}>
                                <div style={{ display: 'flex', border: '1px solid grey', padding: '5px', boxShadow: '0px 1px 1px #00000029' }}>
                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}>B</button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-italic"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-sliders"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-regular fa-image"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-arrow-right"></i></button>
                                    </div>

                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-right"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-center"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-left"></i></button>
                                    </div>

                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-eye"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-check"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-trash"></i></button>

                                    </div>



                                </div>
                                <textarea id="w3review" style={{ resize: 'none', width: '100%' }} name="w3review" rows="4" cols="50"></textarea>
                      </div>
                     </div>

                     <div>
                    <h6>Choice -B</h6>
                    <div style={{ boxShadow: 'rgb(0 0 0 / 16%) 0px 3px 6px' }}>
                                <div style={{ display: 'flex', border: '1px solid grey', padding: '5px', boxShadow: '0px 1px 1px #00000029' }}>
                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}>B</button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-italic"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-sliders"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-regular fa-image"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-arrow-right"></i></button>
                                    </div>

                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-right"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-center"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-left"></i></button>
                                    </div>

                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-eye"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-check"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-trash"></i></button>

                                    </div>



                                </div>
                                <textarea id="w3review" style={{ resize: 'none', width: '100%' }} name="w3review" rows="4" cols="50"></textarea>
                      </div>
                     </div>

            </div>
            <div style={{display:'flex' , justifyContent:'space-around'}}>
            <div>
                    <h6>Choice -C</h6>
                    <div style={{ boxShadow: 'rgb(0 0 0 / 16%) 0px 3px 6px' }}>
                                <div style={{ display: 'flex', border: '1px solid grey', padding: '5px', boxShadow: '0px 1px 1px #00000029' }}>
                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}>B</button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-italic"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-sliders"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-regular fa-image"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-arrow-right"></i></button>
                                    </div>

                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-right"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-center"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-left"></i></button>
                                    </div>

                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-eye"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-check"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-trash"></i></button>

                                    </div>



                                </div>
                                <textarea id="w3review" style={{ resize: 'none', width: '100%' }} name="w3review" rows="4" cols="50"></textarea>
                      </div>
                     </div>

                     <div>
                    <h6>Choice -D</h6>
                    <div style={{ boxShadow: 'rgb(0 0 0 / 16%) 0px 3px 6px' }}>
                                <div style={{ display: 'flex', border: '1px solid grey', padding: '5px', boxShadow: '0px 1px 1px #00000029' }}>
                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}>B</button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-italic"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-sliders"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-regular fa-image"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-arrow-right"></i></button>
                                    </div>

                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-right"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-center"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-align-left"></i></button>
                                    </div>

                                    <div style={{ marginInline: '6px' }}>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-eye"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-check"></i></button>
                                        <button style={{ paddingInline: '10px' }}><i className="fa-solid fa-trash"></i></button>

                                    </div>



                                </div>
                                <textarea id="w3review" style={{ resize: 'none', width: '100%' }} name="w3review" rows="4" cols="50"></textarea>
                      </div>
                     </div>
            </div>

        </Paper>

    </div>
  )
}

export default CaseStudy