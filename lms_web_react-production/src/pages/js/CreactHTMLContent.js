import { useState, useEffect, useRef } from 'react';
// import { CKEditor } from '@ckeditor/ckeditor5-react';
import CloseIcon from '@mui/icons-material/Close';
// import dataStyles from './CKeditorStyle.json';
// import MathType from '@wiris/mathtype-ckeditor5/dist/index.js';
// import '../../Ckeditortemplate.css';


import {
    ClassicEditor,
    AccessibilityHelp,
    Alignment,
    Autoformat,
    AutoImage,
    AutoLink,
    Autosave,
    Base64UploadAdapter,
    BlockQuote,
    Bold,
    Code,
    CodeBlock,
    Essentials,
    FindAndReplace,
    FontBackgroundColor,
    FontColor,
    FontFamily,
    FontSize,
    FullPage,
    GeneralHtmlSupport,
    Heading,
    Highlight,
    HorizontalLine,
    HtmlComment,
    HtmlEmbed,
    ImageBlock,
    ImageCaption,
    ImageInline,
    ImageInsert,
    ImageInsertViaUrl,
    ImageResize,
    ImageStyle,
    ImageTextAlternative,
    ImageToolbar,
    ImageUpload,
    Indent,
    IndentBlock,
    Italic,
    Link,
    LinkImage,
    List,
    ListProperties,
    Markdown,
    MediaEmbed,
    Mention,
    PageBreak,
    Paragraph,
    PasteFromMarkdownExperimental,
    PasteFromOffice,
    RemoveFormat,
    SelectAll,
    ShowBlocks,
    SimpleUploadAdapter,
    SourceEditing,
    SpecialCharacters,
    SpecialCharactersArrows,
    SpecialCharactersCurrency,
    SpecialCharactersEssentials,
    SpecialCharactersLatin,
    SpecialCharactersMathematical,
    SpecialCharactersText,
    Strikethrough,
    Style,
    Subscript,
    Superscript,
    Table,
    TableCaption,
    TableCellProperties,
    TableColumnResize,
    TableProperties,
    TableToolbar,
    TextTransformation,
    TodoList,
    Underline,
    Undo
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';
import { Box, Modal, Typography } from '@mui/material';

export default function CreateHTMLContent() {
    const editorContainerRef = useRef(null);
    const editorRef = useRef(null);
    const [isLayoutReady, setIsLayoutReady] = useState(false);
    const [editorData, setEditorData] = useState('');
    const [fileHandle, setFileHandle] = useState(null);
    const [fileContent, setFileContent] = useState('');
    const [fileSelected,setFileSelected] = useState(false);
    const [openPreview,setOpenPreview] = useState(false);
     const [selectedStyle, setSelectedStyle] = useState();
     const [selectedTemplete, setSelectedTemplate] = useState();
     const [previewData,setPreviewData] = useState('');
     const [styleFromUpload,setStyleFromUpload] = useState('')

     const [iframeSrc, setIframeSrc] = useState('');
     const defultHtmlScript = `  <script>
        const fontSizeSelect = document.getElementById('fontSize');

        // Function to change font size
        function changeFontSize() {
            const selectedSize = fontSizeSelect.value;
            document.body.style.fontSize = selectedSize;  // Changes the body font size
        }

        // Add event listener to the select element
        fontSizeSelect.addEventListener('change', changeFontSize);

        function downloadHTML() {
            // Get the current HTML content
            const htmlContent = document.documentElement.outerHTML;

            // Create a Blob of the HTML content
            const blob = new Blob([htmlContent], { type: 'text/html' });

            // Create a link element to download the blob
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'document.html';  // The file name

            // Append the link to the body, trigger the download, and remove the link
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>`

    const sizeSelect = `  
    <div className="fontChoose">
        <button onclick=downloadHTML()>Print </button>
        <select name="fontSize" id="fontSize">
            <option value="small">Small</option>
            <option value="medium" selected>Medium</option>
            <option value="large">Large</option>
        </select>
    </div>`

  
    const style = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: '100%',
        height:'100%',
        bgcolor: "background.paper",
        // border: "2px solid #000",
        boxShadow: 24,
        borderRadius: 4,
        overflowY:'auto',
        scrollbarWidth: 'thin',
  scrollbarColor:' #888 #f1f1f1', 
        p: 4,
      };
    useEffect(() => {
        setIsLayoutReady(true);

        return () => setIsLayoutReady(false);
    }, []);

     useEffect(() => {
        if (editorData) {
            // Create a blob from the HTML string
            const blob = new Blob([`
                <html>
                <head>
                   <style>
                    ${styleFromUpload}
                   </style
                </head>
                <body>${fileSelected ? '' : sizeSelect}${editorData}${fileSelected ? '' : defultHtmlScript}</body>
                </html>
            `], { type: 'text/html' });

            // Create a URL from the blob
            const url = URL.createObjectURL(blob);
            setIframeSrc(url);

            // Cleanup the URL object when the component unmounts or when editorData changes
            return () => {
                URL.revokeObjectURL(url);
            };
        }
    }, [editorData]);

    const editorConfig = {
        toolbar: {
            items: [
                'undo',
                'redo',
                '|',
                'sourceEditing',
                'showBlocks',
                'findAndReplace',
                '|',
                'heading',
                'style',
                '|',
                'fontSize',
                'fontFamily',
                'fontColor',
                'fontBackgroundColor',
                '|',
                'bold',
                'italic',
                'underline',
                'strikethrough',
                'subscript',
                'superscript',
                'code',
                'removeFormat',
                '|',
                'specialCharacters',
                'horizontalLine',
                'pageBreak',
                'link',
                'insertImage',
                'insertImageViaUrl',
                'mediaEmbed',
                'insertTable',
                'highlight',
                'blockQuote',
                'codeBlock',
                'htmlEmbed',
                '|',
                'alignment',
                '|',
                'bulletedList',
                'numberedList',
                'todoList',
                'outdent',
                'indent',
                // '|',
                // 'MathType',
                // 'ChemType'
            ],
            shouldNotGroupWhenFull: true
        },
        plugins: [
            AccessibilityHelp,
            Alignment,
            Autoformat,
            AutoImage,
            AutoLink,
            Autosave,
            Base64UploadAdapter,
            BlockQuote,
            Bold,
            Code,
            CodeBlock,
            Essentials,
            FindAndReplace,
            FontBackgroundColor,
            FontColor,
            FontFamily,
            FontSize,
            FullPage,
            GeneralHtmlSupport,
            Heading,
            Highlight,
            HorizontalLine,
            HtmlComment,
            HtmlEmbed,
            ImageBlock,
            ImageCaption,
            ImageInline,
            ImageInsert,
            ImageInsertViaUrl,
            ImageResize,
            ImageStyle,
            ImageTextAlternative,
            ImageToolbar,
            ImageUpload,
            Indent,
            IndentBlock,
            Italic,
            Link,
            LinkImage,
            List,
            ListProperties,
            Markdown,
            MediaEmbed,
            Mention,
            PageBreak,
            Paragraph,
            PasteFromMarkdownExperimental,
            PasteFromOffice,
            RemoveFormat,
            SelectAll,
            ShowBlocks,
            SimpleUploadAdapter,
            SourceEditing,
            SpecialCharacters,
            SpecialCharactersArrows,
            SpecialCharactersCurrency,
            SpecialCharactersEssentials,
            SpecialCharactersLatin,
            SpecialCharactersMathematical,
            SpecialCharactersText,
            Strikethrough,
            Style,
            Subscript,
            Superscript,
            Table,
            TableCaption,
            TableCellProperties,
            TableColumnResize,
            TableProperties,
            TableToolbar,
            TextTransformation,
            TodoList,
            Underline,
            Undo,
            // MathType
            
        ],
        fontFamily: {
            supportAllValues: true
        },
        fontSize: {
            options: [10, 12, 14, 'default', 18, 20, 22],
            supportAllValues: true
        },
        heading: {
            options: [
                {
                    model: 'paragraph',
                    title: 'Paragraph',
                    class: 'ck-heading_paragraph'
                },
                {
                    model: 'heading1',
                    view: 'h1',
                    title: 'Heading 1',
                    class: 'ck-heading_heading1'
                },
                {
                    model: 'heading2',
                    view: 'h2',
                    title: 'Heading 2',
                    class: 'ck-heading_heading2'
                },
                {
                    model: 'heading3',
                    view: 'h3',
                    title: 'Heading 3',
                    class: 'ck-heading_heading3'
                },
                {
                    model: 'heading4',
                    view: 'h4',
                    title: 'Heading 4',
                    class: 'ck-heading_heading4'
                },
                {
                    model: 'heading5',
                    view: 'h5',
                    title: 'Heading 5',
                    class: 'ck-heading_heading5'
                },
                {
                    model: 'heading6',
                    view: 'h6',
                    title: 'Heading 6',
                    class: 'ck-heading_heading6'
                }
            ]
        },
        htmlSupport: {
            allow: [
                {
                    name: /^.*$/,
                    styles: true,
                    attributes: true,
                    classes: true
                },
                {
                    name: 'img',
                    attributes: true,
                    classes: true,
                    styles: true
                },
                 {
                    name: 'video',
                    attributes: ['controls', 'width', 'height', 'autoplay', 'muted', 'loop', 'poster'],
                    classes: true,
                    styles: true
                },
                {
                    name: 'source',
                    attributes: ['src', 'type']
                }
            ]
        },
        image: {
            toolbar: [
                'toggleImageCaption',
                'imageTextAlternative',
                '|',
                'imageStyle:inline',
                'imageStyle:wrapText',
                'imageStyle:breakText',
                '|',
                'resizeImage'
            ]
        },
        stylesSet: [    { name: 'Article category', element: 'h3', classes: ['category'] },    { name: 'Side quote', element: 'blockquote', classes: ['quote'] },    { name: 'Marker', element: 'span', classes: ['marker'] } ],
        link: {
            addTargetToExternalLinks: true,
            defaultProtocol: 'https://',
            decorators: {
                toggleDownloadable: {
                    mode: 'manual',
                    label: 'Downloadable',
                    attributes: {
                        download: 'file'
                    }
                }
            }
        },
        list: {
            properties: {
                styles: true,
                startIndex: true,
                reversed: true
            }
        },
        mention: {
            feeds: [
                {
                    marker: '@',
                    feed: [
                        /* See: https://ckeditor.com/docs/ckeditor5/latest/features/mentions.html */
                    ]
                }
            ]
        },
        menuBar: {
            isVisible: true
        },
        placeholder: 'Type or paste your content here!',
        style: {
            definitions: [
                {
                    name: 'Article category',
                    element: 'h3',
                    classes: ['category']
                },
                {
                    name: 'Title',
                    element: 'h2',
                    classes: ['document-title']
                },
                {
                    name: 'Subtitle',
                    element: 'h3',
                    classes: ['document-subtitle']
                },
                {
                    name: 'Info box',
                    element: 'p',
                    classes: ['info-box']
                },
                {
                    name: 'Side quote',
                    element: 'blockquote',
                    classes: ['side-quote']
                },
                {
                    name: 'Marker',
                    element: 'span',
                    classes: ['marker']
                },
                {
                    name: 'Spoiler',
                    element: 'span',
                    classes: ['spoiler']
                },
                {
                    name: 'Code (dark)',
                    element: 'pre',
                    classes: ['fancy-code', 'fancy-code-dark']
                },
                {
                    name: 'Code (bright)',
                    element: 'pre',
                    classes: ['fancy-code', 'fancy-code-bright']
                },
                {
                    name: 'Two Columns',
                    element: 'p',
                    classes: [ 'columns-2' ] // Refers to the CSS class
                },
                {
                    name: 'Three Columns',
                    element: 'p',
                    classes: [ 'columns-3' ] // Refers to the CSS class
                }
            ]
        },
        table: {
            contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties']
        },
        codeBlock: {
            languages: [
                { language: 'javascript', label: 'JavaScript' },
                { language: 'python', label: 'Python' },
                { language: 'html', label: 'HTML' },
                { language: 'css', label: 'CSS' },
                // Add more languages if needed
            ]
        }
    };


    const handlePreview = () => {
        setOpenPreview(!openPreview)
    } 
    const parseHTMLString = (htmlString) => {
        const parser = new DOMParser();
        return parser.parseFromString(htmlString, 'text/html');
      };

    const handleHeaderContents = (contents) => {
        const parsedHTML = parseHTMLString(contents);
        // Extracting head elements
        const headElement = parsedHTML.querySelector('head')?.innerText;
        const metaDescription = parsedHTML.querySelector('meta[name="description"]')?.getAttribute('content');
        // Extracting body content
        const bodyContent = parsedHTML.querySelector('body')?.innerHTML;
        //   const bodyContent = parsedHTML.querySelector('body')?.innerHTML;
        const metaElements = parsedHTML.querySelectorAll('meta');
        const linkElements = parsedHTML.querySelectorAll('link');
        let linkStr = ''
        const linkTags = Array.from(linkElements).map((link) => {

          linkStr += `<link rel="${link.getAttribute('rel')}" href="${link.getAttribute('href')}">`

         let linkDetails = {

          rel: link.getAttribute('rel'),

          href: link.getAttribute('href'),
      }
      return linkDetails

  });
      setStyleFromUpload(styleFromUpload + headElement)
      setEditorData(editorData+bodyContent)

    }
 

    const handleFileOpen = async () => {
        try {
          const [handle] = await window.showOpenFilePicker();
        //   console.log('handle',handle)
          const file = await handle.getFile();
          if (file) {
            const reader = new FileReader();
            setFileSelected(true)
      
            // When the file is read, set its content as CKEditor data
            reader.onload = function (e) {
              const fileContent = e.target.result;
            //   setEditorData(fileContent);
            };
      
            // Read the file as text (for .txt, .html, .doc etc.)
            reader.readAsText(file);
          }
        //   setEditorData()
          const contents = await file.text();
    
          setFileHandle(handle); // Store file handle for saving later
          setFileContent(contents); // Set file content to state for editing
          handleHeaderContents(contents)
        } catch (err) {
        //   console.error('Failed to open file:', err);
        }
      };

      const handleStyleChange = (event) => {
        setSelectedStyle(event.target.value);
        // Your logic to apply the selected style if needed
        // console.log('Selected Style:', event.target.value);
    };

    const handleTemplateChange = (event) => {
        setSelectedTemplate('');
        handleHeaderContents(event.target.value)
        // setEditorData(editorData+event.target.value)
        // Your logic to apply the selected style if needed
        // console.log('Selected Style:', event.target.value);
    };
    const saveAsWordDocument = async () => {
        try {

         if(editorData != '') {
            // const timestamp = new Date().toISOString().replace(/[:.]/g, '-'); // Replace special characters to make the filename valid
            const hexaString  = Date.now().toString(16);
            const fileName = `document-${hexaString}.html`;
            const folderHandle = await window.showDirectoryPicker();
            // console.log('handle',folderHandle)
            const fileHandle = await folderHandle.getFileHandle(fileName, { create: true });
            const writableStream = await fileHandle.createWritable();
            // console.log("testing",dataStyles)
            let htmlContent = ''
            if(fileSelected){
                htmlContent = editorData
            }else{

                htmlContent = `
                    <html xmlns:o='urn:schemas-microsoft-com:office:office' 
                          xmlns:w='urn:schemas-microsoft-com:office:word' 
                          xmlns='http://www.w3.org/TR/REC-html40'>
                    <head><meta charset='utf-8'><style>${styleFromUpload}</style></head><body>${fileSelected ? '' : sizeSelect } ${editorData}${fileSelected ? '' : defultHtmlScript}</body></html>
                `;
            }
            await writableStream.write(htmlContent);
            await writableStream.close();
            setFileSelected(true)
            setFileHandle(fileHandle);
            // console.log('Document saved successfully.');
        }else{
            alert('please type content or select file from device')
        }
        } catch (err) {
            // console.error('Failed to save the document:', err);
        }
      };
    
      // Function for "Save" (automatic file name)
    const handleSave = () => {
        saveAsWordDocument();
      };
      const handleFileSave = async () => {
        if (!fileHandle) return;
    
        try {
          // Get a fresh writable stream every time before saving
          const writableStream = await fileHandle.createWritable();
          let htmlContent = `
          <html xmlns:o='urn:schemas-microsoft-com:office:office' 
                xmlns:w='urn:schemas-microsoft-com:office:word' 
                xmlns='http://www.w3.org/TR/REC-html40'>
          <head><meta charset='utf-8'><style>${styleFromUpload}</style></head><body>${editorData}</body></html>
      `;
          await writableStream.write(htmlContent); // Write the updated content
          await writableStream.close(); // Close the file and finish saving
    
        //   console.log('File updated successfully');
        //   setFileSelected(false)
        //   setEditorData('')
        } catch (err) {
          if (err.name === 'InvalidStateError') {
            // console.error('The file has been changed. Please re-open the file.', err);
          } else {
            // console.error('Failed to save file:', err);
          }
        }
      };

    

    return (
        <div>
            <div className="main-container">
                <div className="editor-container editor-container_classic-editor editor-container_include-style" ref={editorContainerRef}>
                    <div className="editor-container__editor">

                        <div ref={editorRef}>
                            {isLayoutReady 
                                 && 
                            <CKEditor 
                                  editor={ClassicEditor} 
                                  config={editorConfig} 
                                  data={editorData}
                                  onReady={(editor) => {         
                                    editor.plugins.get('ClipboardPipeline').on('inputTransformation', (evt, data) => {             
                                        //console.log('Content pasted from Office:', data.content.getChildren()); 
                                    }); 
                                }}
                                //   disabled={true}
                                  onChange={(event, editor) => {
                                    const data = editor.getData();
                                    // console.log("dddd",data);
                                    setEditorData(data);
                                    const wordContent = `
                                    <html xmlns:o='urn:schemas-microsoft-com:office:office' 
                                          xmlns:w='urn:schemas-microsoft-com:office:word' 
                                          xmlns='http://www.w3.org/TR/REC-html40'>
                                    <head><meta charset='utf-8'><style>${dataStyles.ckEditorStyles}${selectedStyle}</style></head><body>${data}</body></html>
                                `;
                                 setPreviewData(wordContent)
                                  }}
                                  />}
                                  {/* <button onClick={saveAsWord}>Export as Word</button> */}
                                {/* {!fileHandle && 
                                <input type="file" onChange={handleFileUpload} accept=".txt, .html, .doc" />
                                } */}
                                {/* {editorData && <button onClick={handleSave}>Save As</button>} */}
                                {/* <button onClick={handleSaveAs}>Save As</button>
                                <button onClick={handleFileOpen}>Open and Edit File</button> */}
                        </div>
                        <div className='fileHandleMenu'>
                            <button onClick={handleFileOpen}>Open File</button>
                            <button onClick={handlePreview}>Preview & Save</button>
                             <select value={selectedStyle} onChange={handleStyleChange}>
                <option value="">Select Style</option>
                {dataStyles.styles.map((styleDetails) => {
                    return <option value={styleDetails.style}>{styleDetails.name}</option>
                })}
                  </select>
                   <select value={selectedTemplete} onChange={handleTemplateChange}>
                <option value="">Select Templete</option>
                {dataStyles.templates.map((templateDetails) => {
                    return <option value={templateDetails.content}>{templateDetails.name}</option>
                })}
    
            </select>
                        </div>
                        {/* <div>
                            {fileSelected && <button onClick={handleFileSave}>Save File</button>}
                            <button onClick={handleFileOpen}>Open File</button>
                        </div> */}
                    </div>
                </div>
                 <Modal 
            open={openPreview}
            onClose={handlePreview}
            aria-labelledby="modal-modal-title"
            aria-describedby="modal-modal-description"
        >
            <Box sx={style}>
                <div className='closeIconCk'>
                    <div style={{gap:'10px'}}>
                    {fileSelected && <button onClick={handleFileSave}>Save File</button>}
                            {editorData && <button onClick={handleSave}>Save As</button>}
                            </div>
                    <CloseIcon onClick={handlePreview} />
                </div>
                <div>
                    {isLayoutReady && (
                            <div>
                            {/* Injecting the CSS directly into the component */}
                            {/* <style>{selectedStyle+dataStyles.ckEditorStyles}</style> */}
                      
                            {/* Rendering the HTML string */}
                            {/* <div dangerouslySetInnerHTML={{ __html: editorData }} /> */}
                             {editorData && (
                <div>
                    <iframe
                        title="external-content"
                        src={iframeSrc}
                        style={{ width: '100%', height: '500px', border: 'none' }}
                    ></iframe>
                </div>
            )}
                          </div>
                        // <div
                        //     style={{ 
                        //         border: '1px solid #ccc', 
                        //         padding:'30px',
                        //         borderRadius: '5px', 
                        //         height:'100%',
                        //         overflowY: 'auto', // Add scroll if content is large
                        //     }}
                        //     dangerouslySetInnerHTML={{ __html: previewData }} // Render HTML directly
                        // />
                    )}
                </div>
            </Box>
        </Modal>
            </div>
        </div>
    );
}
