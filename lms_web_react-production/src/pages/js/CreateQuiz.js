import React, { useState, useEffect } from "react";
import quizStyles from "./../css/quiz.module.css";
import BlueButton from "../../components/common/BlueButton";
import GreenAddButton from "../../components/common/GreenAddButton";
import WhiteAddButton from "../../components/common/WhiteButton";
import { SearchOutlined } from "@mui/icons-material";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import dashboardContentStyles from "./../css/dashboardContent.module.css";
import subjects_controller_getall from "../API_CONTROLLER/subject_controller/subjects_controller_getall";
import subtopics_controller_getall from "../API_CONTROLLER/subtopic-controller/subtopics_controller_getall";
import chapters_controller_getall from "../API_CONTROLLER/chapters-controller/chapters_controller_get";
import conceptlist_get from "../API_CONTROLLER/concept-controller/conceptlist_get";
import subConcept_controller_get from "../API_CONTROLLER/concept-controller/subConcept_controller_get";
import rootConcept_controller_get from "../API_CONTROLLER/root-concept-controller/rootConcept_controller_get";
import concept_last_modified_get from "../API_CONTROLLER/concept-controller/concept_last_modified_get";
import chapters_list_getall from "../API_CONTROLLER/chapters-controller/chapters_controller_getall";
import concept_controller_post from "../API_CONTROLLER/concept-controller/concept_controller_post";
import concept_table_get from "../API_CONTROLLER/concept-controller/concept_table_get";
import dashHome from "../../../src/img/dashHome.svg";
import searchIcn from "../../../src/img/search.svg";
import BreadCrumbs from "../js/RevisionAdmin/General/BreadCrumbs";
import {
  Box,
  Button,
  FormControl,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
} from "@mui/material";

const CreateQuiz = () => {
  const [showModalShare, setShowModalShare] = useState(false);
  const [subject, setSubject] = useState({});
  const [subtopic, setSubtopic] = useState({});
  const [chapters, setChapters] = useState({});
  const [chaptersList, setChaptersList] = useState({});
  const [conceptlist, setConceptlist] = useState({});
  const [conceptTable, setConceptTable] = useState({});
  const [conceptInputs, setConceptInputs] = useState({
    conceptId: "",
    concept: "",
    rootConcept: "",
    subTopicId: "",
    subjectsId: "",
  });
  const [rootConceptList, setRootConceptList] = useState({});

  const [subConceptInputs, setSubConceptInputs] = useState({
    subConceptId: "",
  });
  const [subconceptList, setSubconceptList] = useState({});

  const [selectedSubject, setSelectedSubject] = useState("");
  const [selectedSubtopic, setSelectedSubtopic] = useState("");
  const [lastUpdated, setLastUpdated] = useState("");
  const [selectedChapter, setSelectedChapter] = useState("");

  const handleShareModal = () => {
    setShowModalShare(true);
  };

  const handleShareModalOk = (e) => {
    setShowModalShare(false);
  };

  const handleConceptInputs = (e) => {
    e.preventDefault();
    setConceptInputs({
      ...conceptInputs,
      [e.target.name]: e.target.value,
    });
  };

  const createConceptHandler = (e) => {
    e.preventDefault();
    concept_controller_post(conceptInputs);
  };

  const handlesubConceptInputs = (e) => {
    e.preventDefault();
    setSubConceptInputs({
      ...subConceptInputs,
      [e.target.name]: e.target.value,
    });
  };

  useEffect(() => {
    subConcept_controller_get(conceptInputs, setSubconceptList);
  }, [conceptInputs]);

  useEffect(() => {
    rootConcept_controller_get(subConceptInputs, setRootConceptList);
  }, [subConceptInputs]);

  useEffect(() => {
    chapters_controller_getall(setChapters);
    subjects_controller_getall(setSubject);
    subtopics_controller_getall(setSubtopic);
    conceptlist_get(setConceptlist);
    concept_last_modified_get(setLastUpdated);

    return () => {};
  }, []);

  useEffect(() => {
    chapters_controller_getall(setChapters);
    chapters_list_getall(setChaptersList);
    concept_table_get(setConceptTable);
  }, []);

  return (
    <>
      <div style={{ width: "100%" }}>
        <article>
        <BreadCrumbs currentPage={"Master Concept"} />
          {/* <div className={dashboardContentStyles.dashboard_link}>
            <span className={dashboardContentStyles.link_icon}>
              <img
                src={dashHome}
                alt="no img"
                width="15px"
                height="20px"
                style={{ verticalAlign: "middle" }}
              />
            </span>
            {"  "}
            <span className={dashboardContentStyles.link_text}>Home</span>
            {"  "}
            <span>
              <i className="fa-solid fa-angle-right"></i>
            </span>
            <span>
              <i className="fa-solid fa-angle-right"></i>
            </span>
            {"  "}
            <a>Master Concept</a>
          </div>
          <div className={dashboardContentStyles.dashboard_last_updated}>
            <p>Last Update: {lastUpdated.data}</p>
          </div> */}
        </article>

        <div>
          <center style={{ marginTop: "95px" }}>
            <p style={{ color: "#354052" }}>Select Quiz Type</p>
            <p
              style={{
                marginBottom: "33px",
                borderBottom: "3px solid #354052",
                width: "56px",
              }}
            ></p>
            <div style={{ display: "flex", justifyContent: "center" }}>
              <div className={quizStyles.quizBox}>
                <p style={{ color: "#354052", fontSize: "1.7vw" }}>Unit Quiz</p>
                <button>Select</button>
              </div>
              <div className={quizStyles.quizBox}>
                <p style={{ color: "#354052", fontSize: "1.7vw" }}>
                  Practice Quiz
                </p>
                <button>Select</button>
              </div>
            </div>
            <div style={{ display: "flex", justifyContent: "center" }}>
              <div className={quizStyles.quizBox}>
                <p style={{ color: "#354052", fontSize: "1.7vw" }}>
                  Annual/Term Quiz
                </p>
                <button>Select</button>
              </div>
              <div className={quizStyles.quizBox}>
                <p style={{ color: "#354052", fontSize: "1.7vw" }}>
                  Diagnostic Quiz
                </p>
                <button>Select</button>
              </div>
            </div>
          </center>
        </div>

        {/* search row start  */}
      </div>
    </>
  );
};

export default CreateQuiz;
