import React, { useState, useEffect } from "react";
import axios from "axios";
import dashboardContentStyles from "./../css/dashboardContent.module.css";
import sectionstyles from "./../css/BranchSection19.module.css";
import { useNavigate } from "react-router-dom";
import School_name from "./school_name"; //school name changer
import Branches_Changer_School_Id from "../API/branch-controller/Branches_Changer_School_Id";
import sectionDataGet from "../API/grade-section-mapping-controller/section-data";
import gradeSectionGet from "../API/grade-section-mapping-controller/grade_section_get";
import sectionListGet from "../API/grade-section-mapping-controller/section_get";
import createGradeSectionMapping from "../API/grade-section-mapping-controller/grade_section_post";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import CommonDeleteModal from "./CommonDeleteModal";
import MappingAlert from "../../components/common/MappingAlert";
import Switch from "../../components/common/Switch";
import { Modal, Box, Typography, Checkbox } from "@mui/material";
import dashHome from "../../../src/img/dashHome.svg";
import { blue } from "@mui/material/colors";
const BranchSectionTesting = () => {
  const [showAlert, setShowAlert] = useState(false);
  const [haveSectionData, setHaveSectionData] = useState([]);
  const [gradeSection, setGradeSection] = useState([]);
  const [sections, setSections] = useState({});
  const [gradeSectionMapping, setGradeSectionMapping] = useState({});
  const [createSection, setCreateSection] = useState(false);
  const [newSetion, setnewSection] = useState("");
  const [tempNewSection, setTempNewSection] = useState("");
  const [newSection, setNewSection] = useState("");
  const [selectedSectionData, setSelectedSectionData] = useState([
    false,
    false,
    true,
  ]);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState({});
  let board_name = sessionStorage.getItem("boardName");
  const schoolid = sessionStorage.getItem("schoolid");
  const branchid = sessionStorage.getItem("branchid");
  const [selectId, setSelectId] = useState([]);
  const [sectionType, setSectionType] = useState("");

  const navigate = useNavigate();

  //BranchController starts
  const [branchList, setBranchList] = useState([]);

  const [schoolDetails, setSchoolDetails] = useState({
    branch: sessionStorage.getItem("branchid"),
  });
  let token = sessionStorage.getItem("token");

  const showAlertHandler = () => {
    // setOpenDeleteModal(true);
    setShowAlert(true);
  };

  const hideAlertHandler = () => {
    setShowAlert(false);
  };

  const showDeleteModal = (id) => {
    setSelectedItem(id);
    setOpenDeleteModal(true);
  };
  const hideDeleteModal = () => {
    setOpenDeleteModal(false);
    setSelectedItem("");
  };

  const handleSectionType = (e) => {
    if (e.target.value === "SAME_SECTION") {
      setSectionType("SAME_SECTION");
    } else if (e.target.value === "NO_SECTION") {
      setSectionType("NO_SECTION");
    } else {
      setSectionType("DIFFERENT_SECTIONS");
    }
  };

  const handleSelect = (value, id) => {
    // console.log("------->fun---->",value,id)
    setnewSection(value);

    setSelectId((prev) => [...prev, id]);
  };

  const handleNewSection = (e, id, type) => {
    if (type) {
      if (e.target.value === "ADD_NEW") {
        const tempData = gradeSection.map((data) =>
          data.id.toString() === id.toString()
            ? { ...data, isSelect: true }
            : data
        );
        // console.log("tempData--->",tempData)
        setGradeSection(tempData);
        setCreateSection(true);
      } else {
        if (e.target.value) {
          setNewSection([e.target.value]);
          setTempNewSection([e.target.value]);
          // console.log(e.target.value);
        } else {
          setCreateSection(true);
        }
      }
    } else {
      const tempData = gradeSection.map((data) =>
        data.id.toString() === id.toString()
          ? { ...data, isSelect: false }
          : data
      );
      setGradeSection(tempData);
    }
  };

  const checkBothMapping = async (elem, e) => {
    const gradeMap = await axios.get(
      `${process.env.REACT_APP_SWAGGER_URL}v1/api/user/grade-section/${elem.gradeId}/grade-mappings`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    let gradeRes = await gradeMap.data.data;
    // console.log(gradeRes, "ffffff");

    const secMap = await axios.get(
      `${process.env.REACT_APP_SWAGGER_URL}v1/api/user/grade-section/${elem.sectionId}/section-mappings`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const sectionRes = await secMap.data.data;
    // console.log(sectionRes, "99999");

    function checkBoth() {
      if (gradeRes && sectionRes) {
        setShowAlert(true);
        return true;
      } else {
        return false;
      }
    }
    return checkBoth();
  };

  const handletoggleSection = (id, e) => {
    let val = e.target.checked;

    axios
      .get(
        `${process.env.REACT_APP_SWAGGER_URL}v1/api/user/grade-section/toggle-active/${id}?active=${val}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      )
      .then((res) => {
        // console.log(res);
        gradeSectionGet(setGradeSectionMapping);
      })
      .catch((err) => console.log(err));
  };

  const handleSectionData = (e) => {
    const updatedSelectedSectionData = selectedSectionData.map(
      (selectedSection, index) => {
        if (index === parseInt(e.target.id)) {
          return true;
        } else {
          return false;
        }
      }
    );
    setSelectedSectionData(updatedSelectedSectionData);
  };

  const handlerSchoolDetailsInputs = (e) => {
    setSchoolDetails({
      ...schoolDetails,
      [e.target.name]: e.target.value,

      // schoolName: e.target.id,
    });
  };

  const addMappingNew = (e, u, tempNewSection) => {
    e.preventDefault();
    // console.log("IN 1111111111", tempNewSection);
    if (sectionType === "SAME_SECTION") {
      var payload = {
        active: true,
        sectionData: "SAME_SECTION",
        schoolId: schoolid,
        branchId: branchid,
        gradeSection: {
          gradeId: allGradeId,
          sectionId: tempNewSection,
        },
      };
    } else if (sectionType === "NO_SECTION") {
      var payload = {
        active: true,
        sectionData: "NO_SECTION",
        schoolId: schoolid,
        branchId: branchid,
        gradeSection: {
          gradeId: [u.id],
        },
      };
    } else {
      var payload = {
        active: true,
        branchId: branchid,
        diffGradeSection: [
          {
            gradeId: u.id,
            sectionId: tempNewSection,
          },
        ],
        schoolId: schoolid,
        sectionData: "DIFFERENT_SECTIONS",
      };
    }
    createGradeSectionMapping(payload, () => {
      setnewSection("");
      getSectionBranchPlan(setGradeSection);
      gradeSectionGet(setGradeSectionMapping);
    });

    // setNewSection([]);
    // gradeSectionGet(setGradeSectionMapping);
  };

  //   branch section grade
  const getSectionBranchPlan = async (setGradeSection) => {
    let data = await axios.get(
      `${
        process.env.REACT_APP_SWAGGER_URL
      }v1/api/master/grades/for-grade-section-mapping?planId=${sessionStorage.getItem(
        "planid"
      )}&boardId=${sessionStorage.getItem("boardid")}`,
      // let data = await axios.get(
      //   `${process.env.REACT_APP_SWAGGER_URL}v1/api/master/grades/for-grade-section-mapping?planId=4028818580b2dcf40180b2e8f2e20000&boardId=ff80818180433e89018043555e2b000e`,

      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${sessionStorage.getItem("token")}`,
        },
      }
    );
    if (data.data.data) {
      const totalElement = data?.data?.totalElements;
      //   if (totalElement >= pageSize) {
      //     setPageSize(totalElement);
      //   }

      setGradeSection(
        data?.data?.data.map((item) => ({ ...item, isSelect: false }))
      );
    }
  };
  const handleTempSection = async (e, u, data) => {
    setTempNewSection([data]);
    await addMappingNew(e, u, [data]);
    // console.log(u,"----data--->",e,data)
  };
  async function gradeSectionGet(setGradeSectionMapping) {
    // let schoolid = sessionStorage.getItem("schoolid");
    // let schoolid = "2c918083811d6bcd01811ec834b10000";

    // let branchid = sessionStorage.getItem("branchid");
    // let branchid = "2c91808381b72b660181ceb9d7af0083";

    let result = await axios
      .get(
        `${process.env.REACT_APP_SWAGGER_URL}v1/api/user/grade-section/all?schoolId=${schoolid}&branchId=${branchid}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      )
      .then((response) => {
        if (response.data.length !== 0) {
          setGradeSectionMapping(response.data.data);
        }
      })
      .catch((err) => console.log(err));
  }

  const handleDeleteSection = (id) => {
    axios
      .delete(
        `${process.env.REACT_APP_SWAGGER_URL}v1/api/user/grade-section/${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      )
      .then((res) => {
        gradeSectionGet(setGradeSectionMapping);
        hideDeleteModal();
      });
  };

  const addSection = (cb) => {
    var data = {
      section: newSetion,
      discription: newSetion,
    };

    return axios
      .post(
        `${process.env.REACT_APP_SWAGGER_URL}v1/api/master/sections`,
        data,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${sessionStorage.getItem("token")}`,
          },
        }
      )
      .then((response) => {
        // console.log("response.data.id----->",response.data.data.id);
        cb(response.data.data.id);
        setNewSection(response.data.data.id);
        setTempNewSection(response.data.data.id);
        // console.log(tempNewSection);
        setCreateSection(false);
        // setModal(false);
      })
      .catch((error) => {
        // console.log(error);
        //   setModal(false);
      });
  };

  useEffect(() => {
    var branchid = schoolDetails.branch;
    sessionStorage.setItem("branchid", branchid);
    Branches_Changer_School_Id(setBranchList);
    getSectionBranchPlan(setGradeSection);
    gradeSectionGet(setGradeSectionMapping);
    return () => {};
  }, [schoolDetails]);

  //BranchController ends
  useEffect(() => {
    sectionDataGet(setHaveSectionData);
    sectionListGet(setSections);
    getSectionBranchPlan(setGradeSection);
    gradeSectionGet(setGradeSectionMapping);
  }, []);

  let gradeSectionArr = [gradeSection];
  const allGradeId = gradeSection?.map((elem) => elem?.id);
  // console.log(allGradeId,"qqqqqq")

  // console.log(gradeSection);

  // console.log(sections);

  // console.log(gradeSectionMapping);

  // console.log(sessionStorage.getItem("planid"));

  // console.log(sessionStorage.getItem("boardid"));

  // console.log(sessionStorage.getItem("token"));

  return (
    <>
      <article>
        <div className={dashboardContentStyles.dashboard_link}>
          <span className={dashboardContentStyles.link_icon}>
            <img
              src={dashHome}
              alt="no img"
              width="15px"
              height="20px"
              style={{ verticalAlign: "middle" }}
            />
          </span>
          {"  "}
          <span
            onClick={showAlertHandler}
            className={dashboardContentStyles.link_text}
          >
            Home
          </span>{" "}
          <span>
            <i className="fa-solid fa-angle-right"></i>
          </span>
          <span>
            <i className="fa-solid fa-angle-right"></i>
          </span>
          {"  "}
          <a>Section Details</a>
        </div>
      </article>

      <article className={sectionstyles.article_add_school_plan}>
        <div className={sectionstyles.Grid_school_name_branch}>
          <div className={sectionstyles.school_name}>
            <School_name />
          </div>
          <select
            className={sectionstyles.branchselect_Dropdown}
            name="branch"
            id="branch"
            value={schoolDetails.branch}
            onChange={handlerSchoolDetailsInputs}
          >
            {branchList.length > 0 ? (
              branchList.map((branchList) => {
                return (
                  <option value={branchList.branchId}>
                    {branchList.branch}
                  </option>
                );
              })
            ) : (
              <option className={sectionstyles.option} value=""></option>
            )}
          </select>
          {/* <button className={sectionstyles.btn_branch_name}>
            AZVASA Mumbai Branch <i className="fa-solid fa-chevron-down"></i>
          </button> */}
        </div>
        <div className={sectionstyles.grid_container_header_plan}>
          <div
            className={sectionstyles.item_plan1}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/branchplan");
            }}
          >
            Plan
          </div>
          <div
            className={sectionstyles.item_plan2}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/branchsection");
            }}
          >
            Sections
          </div>
          <div
            className={sectionstyles.item_plan3}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/teacherId", {
                state: "edit",
              });
            }}
          >
            Academic Staff
          </div>
          <div
            className={sectionstyles.item_plan4}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/student");
            }}
          >
            Students
          </div>
          <div
            className={sectionstyles.item_plan5}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/csp");
            }}
          >
            Change Student Profile
          </div>
          <div
            className={sectionstyles.item_plan6}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/token");
            }}
          >
            Tokens
          </div>
          <div
            className={sectionstyles.item_plan7}
            onClick={(e) => {
              e.preventDefault();
              navigate("/dashboard/logo");
            }}
          >
            Branch Logo
          </div>
        </div>

        <div className={sectionstyles.boards_container}>
          <div className={sectionstyles.grid_item}>
            <label id="boards">Board</label>
            {sessionStorage.getItem("BoardNamex")}
          </div>
          {gradeSectionMapping.length > 0 ? (
            <div
              style={{
                marginLeft: "60px",
                width: "24vw",
                height: "42px",
                backgroundColor: "white",
                zIndex: "1",
                opacity: "0.5",
                position: "absolute",
              }}
            ></div>
          ) : (
            ""
          )}

          {haveSectionData && haveSectionData?.length > 0
            ? haveSectionData.map((sectionData, index) => {
                return (
                  <div
                    className={sectionstyles.radio}
                    key={sectionData.sectionData}
                  >
                    <input
                      id={index}
                      type="checkbox"
                      value={sectionData.sectionData}
                      onChange={(e) => {
                        handleSectionType(e);
                      }}
                      checked={selectedSectionData[index]}
                      onClick={handleSectionData}
                      // disabled={gradeSectionMapping.length > 0}
                    />
                    <label htmlFor="test2">{sectionData.sectionName}</label>
                  </div>
                );
              })
            : ""}

          {/* {haveSectionData.length === 0 ? (
            <p>No Section Data Available</p>
          ) : (
            haveSectionData.map((sectionData, index) => {
              return (
                <div
                  className={sectionstyles.radio}
                  key={sectionData.sectionData}
                >
                  <input
                    id={index}
                    type="checkbox"
                    checked={selectedSectionData[index]}
                    onClick={handleSectionData}
                  />
                  <label for="test2">{sectionData.sectionName}</label>
                </div>
              );
            })
          )} */}
        </div>

        <div className={sectionstyles.section_list_table}>
          <table className={sectionstyles.table_section}>
            <thead>
              <tr className={sectionstyles.table_head_section}>
                {/* <td className={sectionstyles.cbse_checkbox}>
                  <input type="checkbox" id={sectionstyles.test2} />
                  <label for="test2"></label>
                </td> */}

                <td className={sectionstyles.cbse_table_head}>Grades</td>
                <td className={sectionstyles.cbse_table_head1}>Section Name</td>
                <td className={sectionstyles.cbse_table_head2}>Active</td>
                <td className={sectionstyles.cbse_table_head3}>Action</td>
              </tr>
            </thead>
            <tbody>
              {/* Start - render non mapped Grade rows */}
              {/* {console.log(gradeSection, "5555")} */}
              {gradeSection.length > 0 &&
                gradeSection.map((u, ind) => {
                  {
                    /* const myData = grades.data;
                const gradeId = u.id;
                const gradeName = u.grade; */
                  }
                  // if grade mapping already exists then do not render
                  // if (!existingGrades.indexOf(gradeId))
                  return (
                    <>
                      <tr className={sectionstyles.table_row_section}>
                        {/* <td className={sectionstyles.cbse_checkbox}> */}
                        {/* <input type="checkbox" id={sectionstyles.test2} />
                          <label for="test2"></label> */}
                        {/* </td> */}
                        <td className={sectionstyles.cbse_table_head}>
                          {u.grade}
                        </td>
                        <td>
                          {/* {console.log("---------selectId.includes(ind)-->",u.isSelect)} */}
                          {createSection && u.isSelect ? (
                            <>
                              <input
                                type="text"
                                placeholder="Ex: A,B"
                                value={newSetion}
                                onChange={(event) =>
                                  handleSelect(event.target.value, u.id)
                                }
                              />
                              <KeyboardArrowDownIcon
                                onClick={() =>
                                  handleNewSection(null, u.id, false)
                                }
                                style={{
                                  position: "absolute",
                                  fontSize: "30px",
                                  zIndex: "10",
                                }}
                              />
                            </>
                          ) : (
                            //   <>
                            //   {u.section && u.section.length > 0 ?
                            //     ( u.section.map((section) => {
                            //     return(
                            //   <p>{section}</p>
                            //     );
                            //   }))
                            //   :
                            //   < input type='text' placeholder="Ex: A,B"/>
                            // }
                            // </>
                            // )}
                            <select
                              className={sectionstyles.table_data_section}
                              onChange={(e) => handleNewSection(e, u.id, true)}
                              disabled={sectionType === "NO_SECTION"}
                            >
                              <option selected="selected" value={""}></option>
                              <option value={"ADD_NEW"}>New Section</option>

                              {sections && sections.length > 0 ? (
                                sections.map((section) => {
                                  return (
                                    <option value={section.id}>
                                      {section.section}
                                    </option>
                                  );
                                })
                              ) : (
                                <option>select</option>
                              )}
                            </select>
                          )}
                        </td>
                        <td className={sectionstyles.cbse_table_head2}>
                          <Switch
                            checked={u.active}
                            // onChange={(e) =>handletoggleSection(u.id,e)}
                          />
                        </td>
                        <td
                          className={sectionstyles.cbse_table_head3}
                          // id={gradeId}
                        >
                          <button
                            className={sectionstyles.button}
                            onClick={async (e) => {
                              if (createSection) {
                                // console.log("in ifff--------->")
                                await addSection((data) =>
                                  handleTempSection(e, u, data)
                                );
                              } else {
                                await addMappingNew(e, u, tempNewSection);
                              }
                              // console.log("out ifff--------->",tempNewSection)
                            }}
                            id="section_table_btn"
                          >
                            ADD
                          </button>
                        </td>
                      </tr>
                    </>
                  );
                })}

              {gradeSectionMapping.length > 0
                ? gradeSectionMapping.map(
                    (elem) => (
                      // elem.gradeSectionMapping.map((data)=>
                      <tr
                        className={sectionstyles.table_row_section}
                        key={elem.id}
                      >
                        {/* <td className={sectionstyles.cbse_checkbox}>
                        <input type="checkbox" id={data.id} />
                        <label for="test2"></label>
                      </td> */}
                        <td className={sectionstyles.cbse_table_head}>
                          {elem.grade}
                        </td>
                        <td
                          className={sectionstyles.cbse_table_head}
                          style={{ padding: "0px" }}
                        >
                          <div
                            style={{
                              display: "flex",
                              color: "black",
                              flexDirection: "column",
                            }}
                          >
                            {/* {elem.gradeSectionMapping && elem.gradeSectionMapping.length > 0 ? (elem.gradeSectionMapping.map((data, i) => ( */}
                            <p style={{ color: "black" }}>{elem.section}</p>
                            {/* ))) : <p>-</p>} */}
                          </div>

                          {/* {grade_section_map.sectionName} */}
                        </td>
                        <td
                          className={sectionstyles.cbse_table_head2}
                          style={{ padding: "0px" }}
                        >
                          <Switch
                            checked={elem.active}
                            onChange={
                              (e) => {
                                checkBothMapping(elem, e)
                                  ? setShowAlert(true)
                                  : handletoggleSection(elem.id, e);
                              }
                              // handletoggleSection(elem.id,e)
                            }
                          />
                          {/* <label className={sectionstyles.active_switch}>
                          <input
                            // id={grade_section_map.id}
                            type="checkbox"
                            // checked={!grade_section_map.active}
                            // onClick={(e) => (handleToggle(e, res))}
                          />
                          <span
                            className={sectionstyles.active_slider}
                          ></span>
                        </label>{" "} */}
                        </td>

                        <td className={sectionstyles.cbse_table_head3}>
                          <div className={sectionstyles.wrapper}>
                            <div className={sectionstyles.icon}>
                              <div className={sectionstyles.tooltip}>
                                Add section
                              </div>
                              <span
                              // onClick={addGradeRow}
                              >
                                <i
                                  className="fa-regular fa-circle-plus"
                                  // onClick={props.onClickHandler}
                                  id="section_add_more_btn"
                                ></i>
                              </span>
                            </div>
                            <div className={sectionstyles.icon}>
                              <div className={sectionstyles.tooltip}>Edit</div>
                              <span
                              // id={gradeId}
                              >
                                <i
                                  className="fa-solid fa-pencil"
                                  // onClick={handleUpdate}
                                ></i>
                              </span>
                            </div>
                            <div
                              onClick={(e) => {
                                checkBothMapping(elem, e)
                                  ? setShowAlert(true)
                                  : showDeleteModal(elem);
                              }}
                              className={sectionstyles.icon}
                            >
                              <div className={sectionstyles.tooltip}>
                                Remove
                              </div>
                              <span

                              // id={grade_section_map.id}
                              // onClick={(res)=>{handleDelete(res)}}
                              >
                                <i className="fa-solid fa-trash-can"></i>
                              </span>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )

                    // )
                  )
                : ""}
              {/* End - render non mapped Grade rows */}
              {/* {console.log(gradeSection,"eeeeeeeee")} */}
            </tbody>
          </table>
        </div>
        <CommonDeleteModal
          open={openDeleteModal}
          close={hideDeleteModal}
          deleteTitle={"Section"}
          deleteHandler={handleDeleteSection}
          deleteId={selectedItem?.id}
          deleteName={selectedItem?.section}
        />
        {/* alert */}
        <MappingAlert
          open={showAlert}
          onClose={hideAlertHandler}
          close={hideAlertHandler}
          alert="This Grade-section has other child links"
        />
        {/* <Modal
        // open={showAlertHandler}
        open={true}
        onClose={hideAlertHandler}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box style={{position:'absolute',left:'50%',top:'50%',width:"400px",height:'250px' , backgroundColor:'white'}} >
          <Typography id="modal-modal-title" variant="h6" component="h2">
            Unable to Deactivate
          </Typography>
          <Typography id="modal-modal-description" sx={{ mt: 2 }}>
            Grade/section are mapped to other stuffs
          </Typography>
          <button onClick={setShowAlert(false)}>OK</button>
        </Box>
      </Modal> */}
      </article>
    </>
  );
};

export default BranchSectionTesting;
