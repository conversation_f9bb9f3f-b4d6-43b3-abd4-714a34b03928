package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.RoleMenuMapRequestDto;
import com.intelliread.response.dto.MenuSubMenuResponseDto;
import com.intelliread.response.dto.RoleMenuMapResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.RoleMenuMapService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/v1/api/master/role-menu-map")
public class RoleMenuMapController {
	
	@Autowired
	private RoleMenuMapService roleMenuMapService;
	
	@PostMapping()
	@SuppressWarnings("unchecked")
	public LMSResponse<RoleMenuMapResponseDto> createRoleMenuMaps(@RequestBody RoleMenuMapRequestDto request) {
		RoleMenuMapResponseDto response = roleMenuMapService.createRoleMenuMap(request);
		return ResponseHelper.createResponse(new LMSResponse<RoleMenuMapResponseDto>(), response,
				Translator.toLocale("role.menu.created.success", null),
				Translator.toLocale("role.menu.create.failed", null));
	}
	
	/**
	 * Designed this API for feign call in USERS-SERVICE
	 * @param roles
	 * @return
	 */
	@GetMapping("/by/{roles}")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<MenuSubMenuResponseDto>> getAllRolesMenuMappingByRoles(@PathVariable("roles") List<String> roles) {
		List<MenuSubMenuResponseDto> response = roleMenuMapService.getAllRolesMenuMappingByRoles(roles);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<MenuSubMenuResponseDto>>(), response,
				Translator.toLocale("role.menu.fetch.success", null),
				Translator.toLocale("role.menu.fetch.failed", null));
	}
}
