package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.GradingMappingRequest;
import com.intelliread.response.dto.IrGradingMappingResponse;
import com.intelliread.response.dto.ListIrGradingMappingResponse;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.GradingMappingService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping(value = "/v1/api/grading-mapping")
public class GradingMappingController {
	
	@Autowired
	GradingMappingService gradingMappingService;
	
	@PostMapping()
	@SuppressWarnings("all")
	public LMSResponse<IrGradingMappingResponse> createGradingMapping(@Valid @RequestBody GradingMappingRequest dto){
		IrGradingMappingResponse response= gradingMappingService.createGradingMapping(dto);
		return  ResponseHelper.createResponse(new LMSResponse<IrGradingMappingResponse>(), response,
				Translator.toLocale("irgradingmapping.created.success", null),
				Translator.toLocale("irgradingmapping.create.failed", null));
	}
	
	
	@GetMapping()
	@SuppressWarnings("all")
	public LMSResponse<ListIrGradingMappingResponse> getAllGradingMapping(){
		ListIrGradingMappingResponse response= gradingMappingService.getAllGradingMapping();
		return  ResponseHelper.createResponse(new LMSResponse<ListIrGradingMappingResponse>(), response,
				Translator.toLocale("irgradingmapping.retrieved.success", null),
				Translator.toLocale("irgradingmapping.retrieve.failed", null));
	}
	
	
	@GetMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<IrGradingMappingResponse> getGradingMapping(@Valid @PathVariable("id") String id){
		IrGradingMappingResponse response= gradingMappingService.getGradingMapping(id);
		return  ResponseHelper.createResponse(new LMSResponse<IrGradingMappingResponse>(), response,
				Translator.toLocale("irgradingmapping.retrieved.success", null),
				Translator.toLocale("irgradingmapping.retrieve.failed", null));
	}


}
