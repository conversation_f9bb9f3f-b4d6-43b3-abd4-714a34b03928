package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.feign.master.MastersFeignClient;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.PaginatedResponse;
import com.intelliread.repository.users.StudentsRepository;
import com.intelliread.repository.users.TeacherRepository;
import com.intelliread.request.dto.*;
import com.intelliread.response.dto.*;
import com.intelliread.services.UserService;
import com.intelliread.utilities.Constants;
import com.intelliread.utilities.ResponseHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * Processes an {@link UserController} request.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/api/user/users")
public class UserController {

    private static final Logger log = LoggerFactory.getLogger(UserController.class);
    @Autowired
    private UserService userService;

    @Autowired
    TeacherRepository teacherRepository;

    @Autowired
    StudentsRepository studentRepository;

    @Autowired
    MastersFeignClient mastersFeignClient;

    @Lazy
    @PostMapping()
    @SuppressWarnings("unchecked")
    public LMSResponse<UsersResponseDto> createUser(@Valid @RequestBody UsersRequestDto request) {
        UsersResponseDto response = userService.createuser(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.created.successfully", null),
                Translator.toLocale("user.create.failed", null));
    }

    @Lazy
    @PutMapping("/{id}")
    @SuppressWarnings("unchecked")
    public LMSResponse<UsersResponseDto> updateUser(@PathVariable("id") String id,
                                                    @Valid @RequestBody UsersRequestDto request) {
        UsersResponseDto response = userService.updateUser(id, request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.updated.successfully", null),
                Translator.toLocale("user.update.failed", null));
    }

    @Lazy
    @GetMapping("/{id}")
    @SuppressWarnings("unchecked")
    public LMSResponse<UsersResponseDto> getUserById(@PathVariable("id") String id) {
        UsersResponseDto response = userService.getuserById(id);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.get.by.id.success", null),
                Translator.toLocale("user.get.by.id.failed", null));
    }

    /**
     * Feign call for the other services
     *
     * @param username
     * @return
     */
    @Lazy
    @GetMapping("/username/{username}")
    @SuppressWarnings("unchecked")
    public LMSResponse<UsersFeignDto> getUsersByUsernameForFeign(@PathVariable("username") String username) {
        UsersFeignDto response = userService.getUsersByUsernameForFeign(username);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<UsersFeignDto>(), response,
                Translator.toLocale("user.get.by.id.success", null),
                Translator.toLocale("user.get.by.id.failed", null));
    }
    
    @Lazy
    @GetMapping("/userDetails/{username}")
    @SuppressWarnings("unchecked")
    public LMSResponse<UsersResponseDto> getUserDetailsForFeign(@PathVariable("username") String username) {
    	UsersResponseDto response = userService.getUserDetailsForFeign(username);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.get.by.id.success", null),
                Translator.toLocale("user.get.by.id.failed", null));
    }

    @Lazy
    @GetMapping()
    @SuppressWarnings("unchecked")
    public LMSResponse<Page<UsersResponseDto>> getUsersByPagniation(
            @RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
            @RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
            @RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
            @RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
            @RequestParam(value = "search", required = false) String search,
            @RequestParam(value = "roleId", required = false) String roleId,
            @RequestParam(value = "active", required = false) Boolean active) {
        PaginatedResponse<UsersResponseDto> response = userService.getUsersByPagniation(pageNumber, pageSize, sortOrder,
                sortBy, search, roleId, active);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<UsersResponseDto>>(), response,
                Translator.toLocale("user.get.all.success", null), Translator.toLocale("user.get.all.failed", null));
    }

    /**
     * Processes an {resetPassword} request. This API required authorisation token.
     * Call this API after the login.
     *
     * @param id
     * @return Password Reset Success or Failure
     * @RequestBody UsersResetPasswordDto
     */
    @Lazy
    @PutMapping("/resetpassword/{id}")
    @SuppressWarnings({"unchecked", "all"})
    public LMSResponse<UsersResponseDto> resetPassword(@PathVariable("id") String id,
                                                       @Valid @RequestBody UsersResetPasswordDto request) {
        UsersResponseDto response = userService.resetPassword(id, request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("password.reset.scucessfully", null),
                Translator.toLocale("password.reset.failed", null));
    }

    @Lazy
    @GetMapping("/check-mapping")
    @SuppressWarnings("unchecked")
    public LMSResponse<String> checkTheMappingExistBeforeDeleteOrTogglingActive(@RequestParam(value = "id") String id,
                                                                                @RequestParam(value = "operationType") String operationType) {
        String response = userService.checkTheMappingExistBeforeDeleteOrTogglingActive(id, operationType);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("request.handled.success", null),
                Translator.toLocale("something.went.wrong", null));
    }

    /**
     * Processes forgot password by email.
     * <p>
     * This API no need of authorisation token. Used for resetting a forgotten
     * password through email
     *
     * @param email Email Service will send reset Password Link to given email
     * @return Email Send Success or Failure
     */
    @Lazy
    @GetMapping("/forgot-password")
    @SuppressWarnings("unchecked")
    public LMSResponse<EmailRequestDto> createForgotPasswordAndSendEmail(
            @RequestParam("email") @NotBlank(message = Constants.MANDATORY_FIELD) String email,
            @RequestParam("lmsEnv") @NotBlank(message = Constants.MANDATORY_FIELD) String lmsEnv,
            @RequestParam(value = "userName", required = true) String userName, HttpServletRequest request) {
        EmailRequestDto response = userService.forgotPassword(email, lmsEnv, userName);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<EmailRequestDto>(), response,
                Translator.toLocale("email.sent.successfully", null), Translator.toLocale("email.sent.failed", null));
    }

    /**
     * API no need of authorisation token. Call this API followed by the API
     * /forgot-password. Take the password and encrypted userId from the request
     *
     * @param request
     * @return
     */
    @Lazy
    @PutMapping("/reset-password")
    @SuppressWarnings("unchecked")
    public LMSResponse<UsersResponseDto> withoutTokenResetPassword(
            @Valid @RequestBody ResetPasswordWithoutTokenRequestDto request) {
        UsersResponseDto response = userService.withoutTokenResetPassword(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("password.reset.scucessfully", null),
                Translator.toLocale("password.reset.failed", null));
    }

    /**
     * By MOBILE API no need of authorisation token. Call this API if the forgot
     * password functionality is execute by the mobile number.
     *
     * @param phoneNumber
     * @param request
     * @return
     */
    @Lazy
    @GetMapping("/forgotpasswordMobile")
    @SuppressWarnings("unchecked")
    public LMSResponse<String> forgotPasswordMobile(@RequestParam("phoneNumber") String phoneNumber,
                                                    @RequestParam(value = "userName", required = true) String userName, HttpServletRequest request) {
        String response = userService.forgotPasswordMobile(phoneNumber, userName);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("user.otp.send.succes", null), Translator.toLocale("user.otp.send.failed", null));
    }

    /**
     * By MOBILE This API no need of the authorisation token. Call this API followed
     * by the API /forgotpasswordMobile Take the phoneNumnber and received OTP
     *
     * @param request
     * @return
     */
    @Lazy
    @PutMapping("/verify-otp")
    @SuppressWarnings("unchecked")
    public LMSResponse<UsersResponseDto> verifyOtp(@Valid @RequestBody VerifyOtpRequestDto request) {
        UsersResponseDto response = userService.verifyOtp(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.otp.verified.success", null),
                Translator.toLocale("user.otp.verified.failed", null));
    }

    /**
     * By MOBILE This API no need of the authorisation. Call this API followed by
     * /verify-otp Take the userId (from response of above API) and the password
     *
     * @param request
     * @return
     */
    @Lazy
    @PutMapping("/reset-password-mobile")
    @SuppressWarnings("unchecked")
    public LMSResponse<UsersResponseDto> mobileResetPasswordWithoutToken(
            @Valid @RequestBody ResetPasswordWithoutTokenRequestDto request) {
        UsersResponseDto response = userService.mobileResetPasswordWithoutToken(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("password.reset.scucessfully", null),
                Translator.toLocale("password.reset.failed", null));
    }

    @GetMapping("/last-modified-at")
    @SuppressWarnings("unchecked")
    public LMSResponse<String> getLastModifiedAt() {
        String response = userService.getLastModifiedAt();
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("last.modified.time.fetch.success", null),
                Translator.toLocale("last.modified.time.fetch.failed", null));
    }

    @GetMapping("/role-mapping/{roleId}")
    @SuppressWarnings("unchecked")
    public LMSResponse<List<UserMinResponseDto>> getAllUserResponsesForRole(@PathVariable("roleId") String roleId) {
        List<UserMinResponseDto> response = userService.getAllUsersMappedToRole(roleId);
        return ResponseHelper.createResponse(new LMSResponse<UserMinResponseDto>(), response,
                Translator.toLocale("request.handled.success", null),
                Translator.toLocale("something.went.wrong", null));

    }

    /**
     * @param username
     * @param roles
     * @return
     */
    @GetMapping("/profile")
    @SuppressWarnings("unchecked")
    public LMSResponse<ProfileResponseDto> getUsersByUsername(
            @RequestParam(value = "username", required = true) String username,
            @RequestParam(value = "roles", required = true) List<String> roles) {
        ProfileResponseDto response = userService.getUsersByUserName(username, roles);
        return ResponseHelper.createResponse(new LMSResponse<ProfileResponseDto>(), response,
                Translator.toLocale("user.get.by.username.and.role.success", null),
                Translator.toLocale("user.get.by.username.and.role.failed", null));
    }

    @Lazy
    @GetMapping("/count-role-id")
    @SuppressWarnings("unchecked")
    public LMSResponse<Long> countRoleIdForDeletion(@RequestParam("roleId") String roleId) {
        Long response = userService.countRoleIdForDeletion(roleId);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<Long>(), response,
                Translator.toLocale("role.id.count.success", null), Translator.toLocale("role.id.count.failed", null));
    }

    @GetMapping("/ids-by-role-id")
    @SuppressWarnings("unchecked")
    public LMSResponse<List<String>> getUserIdsByRoleId(@RequestParam("roleId") String roleId) {
        List<String> response = userService.getUserIdsByRoleId(roleId);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
                Translator.toLocale("user.ids.fetch.success", null),
                Translator.toLocale("user.ids.fetch.failed", null));
    }

    @Lazy
    @PostMapping("/share-details")
    @SuppressWarnings("unchecked")
    public LMSResponse<ShareDetailsResponseDto> createShareDetailsAndSendEmail(
            @Valid @RequestBody ShareDetailsRequestDto request) {
        ShareDetailsResponseDto response = userService.shareUserDetails(request);
        return ResponseHelper.createResponse(new LMSResponse<ShareDetailsResponseDto>(), response,
                Translator.toLocale("user.details.share.success", null),
                Translator.toLocale("user.details.share.failed", null));
    }

    @Lazy
    @PutMapping("/update-password")
    @SuppressWarnings("unchecked")
    public LMSResponse<CreateUserEmailRequestDto> createPasswordAndSendEmail(
            @Valid @RequestBody ChangePasswordRequestDto request) {
        CreateUserEmailRequestDto response = userService.changePasswordBySuperAdmin(request);
        return ResponseHelper.createResponse(new LMSResponse<CreateUserEmailRequestDto>(), response,
                Translator.toLocale("password.updated.successfully", null),
                Translator.toLocale("password.update.failed", null));
    }

    @PutMapping("/change-mobile-number/generate-otp")
    @SuppressWarnings("unchecked")
    public LMSResponse<String> generateOtp(@Valid @RequestBody UpdateMobileNumberRequestDto request) {
        String response = userService.generateOtp(request);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("user.otp.send.succes", null), Translator.toLocale("user.otp.send.failed", null));
    }

    @Lazy
    @PutMapping("/change-mobile-number/verify-otp")
    @SuppressWarnings("unchecked")
    public LMSResponse<UsersResponseDto> verifyOtpToChangeMobileNumber(
            @Valid @RequestBody UpdateMobileNumberRequestDto request) {
        UsersResponseDto response = userService.verifyOtpToChangeMobileNumber(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.mobileNumber.update.success", null),
                Translator.toLocale("user.mobileNumber.update.failed", null));
    }

    @GetMapping("/extracting-user-name")
    @SuppressWarnings("unchecked")
    public LMSResponse<List<String>> getUserNameExtractingList(
            @RequestParam(value = "extractor", required = true) String extractor) {
        List<String> response = userService.getUserNameExtractingList(extractor);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
                Translator.toLocale("user.details.fetch.success", null),
                Translator.toLocale("user.details.fetch.failed", null));
    }

    @GetMapping("/user-details/by-role")
    @SuppressWarnings("unchecked")
    public LMSResponse<List<UsersRoleResponseDto>> getAllAdminUsersRole(
            @RequestParam(value = "roleId", required = false) List<String> roleIds) {
        List<UsersRoleResponseDto> response = userService.getAllAdminUsersRole(roleIds);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<UsersRoleResponseDto>>(), response,
                Translator.toLocale("user.details.fetch.success", null),
                Translator.toLocale("user.details.fetch.failed", null));
    }

    @Lazy
    @GetMapping("/users-count")
    @SuppressWarnings("unchecked")
    public LMSResponse<List<UsersUseWebMobCountResponseDto>> getUserWebMobCount() {
        List<UsersUseWebMobCountResponseDto> response = userService.getUserWebMobCount();
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<UsersUseWebMobCountResponseDto>>(), response,
                Translator.toLocale("user.check.in.history.total.count.success", null),
                Translator.toLocale("user.check.in.history.total.count.failed", null));
    }

    @Lazy
    @GetMapping("/name-by-username")
    @SuppressWarnings("unchecked")
    public LMSResponse<List<UserMinResponseDto>> getAllUsersByUserNames(
            @RequestParam("userNames") List<String> userNames) {
        List<UserMinResponseDto> response = userService.getAllUsersByUserNames(userNames);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<UserMinResponseDto>>(), response,
                Translator.toLocale("user.name.success", null),
                Translator.toLocale("user.name.failed", null));
    }

    @Lazy
    @PutMapping("/forgot-password/generate-otp")
    @SuppressWarnings("unchecked")
    public LMSResponse<String> userSendOtp(
    		 @Valid @RequestBody ForgetPasswordNumberOtpRequestDto request) {
        String response = userService.userSendOtp(request);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("user.otp.send.succes", null),
                Translator.toLocale("user.otp.send.failed", null));
    }
    
    @Lazy
    @PutMapping("/forgot-password/verify-otp")
    @SuppressWarnings("unchecked")
    public LMSResponse<String> userVerifyOtp(
    		@Valid @RequestBody ForgetPasswordOtpRequestDto request) {
        String response = userService.userVerifyOtp(request);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("user.otp.verified.success", null),
                Translator.toLocale("user.otp.verified.failed", null));
    }
    
    @GetMapping("/extracting-user-name-with-user-type")
    @SuppressWarnings("unchecked")
    public LMSResponse<List<String>> getUserNameExtractingListWithUserType(
            @RequestParam(value = "extractor", required = true) String extractor,
            @RequestParam(value = "userType", required = true) String userType) {
        List<String> response = userService.getUserNameExtractingListWithUserType(extractor,userType);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
                Translator.toLocale("user.details.fetch.success", null),
                Translator.toLocale("user.details.fetch.failed", null));
    }
}
