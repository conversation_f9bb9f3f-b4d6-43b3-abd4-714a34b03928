package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.entity.master.GradingModel;
import com.intelliread.request.dto.GradingModelRequest;
import com.intelliread.response.dto.DeleteGradingModelResponse;
import com.intelliread.response.dto.GradingModelResponse;
import com.intelliread.response.dto.ListGradingModelResponse;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.PaginatedResponse;
import com.intelliread.services.GradingModelService;
import com.intelliread.utilities.ResponseHelper;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/v1/api/master/gradingModel")
public class GradingModelController {
	
	@Autowired
	GradingModelService gradingModelService;
	
	@PostMapping()
	@SuppressWarnings("all")
	public LMSResponse<GradingModelResponse> createGradingModel(@Valid @RequestBody GradingModelRequest request) {
		GradingModelResponse response = gradingModelService.createGradingModel(request);
		return ResponseHelper.createResponse(new LMSResponse<GradingModelResponse>(), response,
				Translator.toLocale("gradingmodel.created.success", null),
				Translator.toLocale("gradingmodel.create.failed", null));
	}
	
	@PutMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<GradingModelResponse> updateGradingModel(@PathVariable("id") String id,@Valid @RequestBody GradingModelRequest request) {
		GradingModelResponse response = gradingModelService.updateGradingModel(id,request);
		return ResponseHelper.createResponse(new LMSResponse<GradingModelResponse>(), response,
				Translator.toLocale("gradingmodel.updated.success", null),
				Translator.toLocale("gradingmodel.update.failed", null));
	}
	
	@DeleteMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<DeleteGradingModelResponse> deleteGradingModel(@PathVariable("id") String id) {
		DeleteGradingModelResponse response = gradingModelService.deleteGradingModel(id);
		return ResponseHelper.createResponse(new LMSResponse<DeleteGradingModelResponse>(), response,
				Translator.toLocale("gradingmodel.deleted.success", null),
				Translator.toLocale("gradingmodel.delete.failed", null));
	}
	
	@GetMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<GradingModelResponse> getGradingModel(@PathVariable("id") String id) {
		GradingModelResponse response = gradingModelService.getGradingModel(id);
		return ResponseHelper.createResponse(new LMSResponse<GradingModelResponse>(), response,
				Translator.toLocale("gradingmodel.retrieved.success", null),
				Translator.toLocale("gradingmodel.retrieve.failed", null));
	}
	
	@GetMapping()
	@SuppressWarnings("all")
	public LMSResponse<PaginatedResponse> getAllGradingModel(
			@RequestParam(value = "pageNo", defaultValue = "0", required = true) @Min(0) int pageNo,
			@RequestParam(value = "pageSize", defaultValue = "10", required = true) @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "false") boolean sortOrder
	) {
		PaginatedResponse<GradingModelResponse> response = gradingModelService.getAllGradingModel(pageNo, pageSize, sortOrder);
		return ResponseHelper.createResponse(new LMSResponse<PaginatedResponse>(), response,
				Translator.toLocale("gradingmodel.retrieved.success", null),
				Translator.toLocale("gradingmodel.retrieve.failed", null));
	}

}
