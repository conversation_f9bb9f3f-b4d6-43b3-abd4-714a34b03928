package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.entity.users.Users;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.request.dto.AuthRequestDto;
import com.intelliread.request.dto.ValidateTokenRequestDto;
import com.intelliread.response.dto.AuthResponseDto;
import com.intelliread.response.dto.UsersResponseDto;
import com.intelliread.services.UserService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * Processes an {@link AuthController} request.
 * <p>
 * 1. {@code generateToken} to login & authenticate.
 * <p>
 * 2. {@code refreshtoken} to refresh the authentication token thus extend the
 * login time.
 * <p>
 * 3. {@code tokenValidation} to validate the token, check the token is valid &
 * expire time is exceed etc
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/api/user/authenticate")
public class AuthController {

	@Autowired
	private UserService userService;

	/**
	 * Processes an {@link generate JWT Token} request.
	 * 
	 * @RequestBody AuthRequestDto
	 * @return Generated JWT Token
	 * 
	 */
	@SuppressWarnings("unchecked")
	@PostMapping()
	@Lazy
	public LMSResponse<AuthResponseDto> generateToken(@Valid @RequestBody AuthRequestDto authRequest,
			HttpServletRequest request) {
		AuthResponseDto authResponse = userService.authentication(authRequest);
		return ResponseHelper.createResponse(new LMSResponse<AuthResponseDto>(), authResponse,
				Translator.toLocale("user.authenticated.scucessfully", null),
				Translator.toLocale("user.authentication.failed", null));

	}

	/**
	 * Processes an {@link generate JWT Refresh Token} request.
	 * 
	 * @RequestBody AuthRequestDto
	 * @return Generated JWT Refresh Token
	 * 
	 */
	@SuppressWarnings("unchecked")
	@PostMapping("/extendtoken")
	@Lazy
	public LMSResponse<AuthResponseDto> refreshtoken(HttpServletRequest request) {
		AuthResponseDto authResponse = userService.authenticationByRefreshToken(request);
		return ResponseHelper.createResponse(new LMSResponse<AuthResponseDto>(), authResponse,
				Translator.toLocale("user.authenticated.scucessfully", null),
				Translator.toLocale("user.authentication.failed", null));
	}

	/**
	 * Processes an {@link Validate JWT Token} request.
	 * 
	 * @RequestBody AuthRequestDto
	 * @return Generated JWT Token
	 * 
	 */
	@SuppressWarnings("unchecked")
	@PostMapping("/validatetoken")
	@Lazy
	public LMSResponse<UsersResponseDto> tokenValidation(@Valid @RequestBody ValidateTokenRequestDto request) {
		UsersResponseDto response = userService.tokenValidation(request);
		return ResponseHelper.createResponse(new LMSResponse<Users>(), response,
				Translator.toLocale("user.authenticated.scucessfully", null),
				Translator.toLocale("user.authentication.failed", null));
	}

}
