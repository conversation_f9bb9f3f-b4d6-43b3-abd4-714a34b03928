package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.*;
import com.intelliread.response.dto.GetQuestionBankList;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.PaginatedResponse;
import com.intelliread.services.QuestionBankService;
import com.intelliread.utilities.ResponseHelper;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;


@RestController
@RequestMapping("/v1/api/master/bank")
public class QuestionBankController {
	
	@Autowired
	QuestionBankService questionBankService;
	
	@PostMapping()
	@SuppressWarnings("all")
	public LMSResponse<QuestionBankResponseDto> createQuestionBank(@RequestParam(value="id",required=false) String id,@Valid @RequestBody QuestionBankRequestDto dto){
		QuestionBankResponseDto response=questionBankService.createQuestionBank(id,dto);
		return  ResponseHelper.createResponse(new LMSResponse<QuestionBankResponseDto>(), response,
				Translator.toLocale("questionbank.created.success", null),
				Translator.toLocale("questionbank.create.failed", null));
	}
	
	
	@PutMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<QuestionBankResponseDto> updateQuestionBank(@Valid @PathVariable("id") String id,@Valid @RequestBody QuestionBankRequestDto dto){
		QuestionBankResponseDto response=questionBankService.updateQuestionBank(id,dto);
		return  ResponseHelper.createResponse(new LMSResponse<QuestionBankResponseDto>(), response,
				Translator.toLocale("questionbank.updated.success", null),
				Translator.toLocale("questionbank.update.failed", null));
	}
	
	
	//Todo Only if no Assessments - This will be physically deleted
	@DeleteMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<DeleteQuestionBankResponse> deleteQuestionBank(@Valid @PathVariable("id") String id){
		DeleteQuestionBankResponse response=questionBankService.deleteQuestionBank(id);
		return  ResponseHelper.createResponse(new LMSResponse<DeleteQuestionBankResponse>(), response,
				Translator.toLocale("questionbank.deleted.success", null),
				Translator.toLocale("questionbank.deleted.failed", null));
	}
	
	
	@PutMapping("/{id}/{status}")
	@SuppressWarnings("all")
	public LMSResponse<UpdateQuestionBankStatusResponse> updateQuestionBankStatus(@Valid @PathVariable("id") String id,@Valid @PathVariable("status") String status){
		UpdateQuestionBankStatusResponse response=questionBankService.updateQuestionBankStatus(id,status);
		return  ResponseHelper.createResponse(new LMSResponse<UpdateQuestionBankStatusResponse>(), response,
				Translator.toLocale("questionbank.status.updated.success", null),
				Translator.toLocale("questionbank.status.update.failed", null));
	}
	
	
	@GetMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<GetQuestionBankResponse> getQuestionBank(@Valid @PathVariable("id") String id){
		GetQuestionBankResponse response=questionBankService.getQuestionBank(id);
		return  ResponseHelper.createResponse(new LMSResponse<GetQuestionBankResponse>(), response,
				Translator.toLocale("questionbank.retrieved.success", null),
				Translator.toLocale("questionbank.retrieve.failed", null));
	}
	
	@GetMapping("/group/{groupId}")
	@SuppressWarnings("all")
	public LMSResponse<GetQuestionBankList> getQuestionBankByGroupId(@PathVariable("groupId") String groupId){
		GetQuestionBankList response=questionBankService.getQuestionBankByGroupId(groupId);
		return  ResponseHelper.createResponse(new LMSResponse<GetQuestionBankList>(), response,
				Translator.toLocale("questionbank.retrieved.success", null),
				Translator.toLocale("questionbank.retrieve.failed", null));

	}
	
	@GetMapping()
	@SuppressWarnings("all")
	public LMSResponse<PaginatedResponse> getAllQuestionBank(
			@RequestParam(value = "pageNo", defaultValue = "0", required = true) @Min(0) int pageNo,
			@RequestParam(value = "pageSize", defaultValue = "10", required = true) @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "false") boolean sortOrder,
			@RequestParam(value="courseId",required=false) String courseId,
			@RequestParam(value="sectionId",required=false) String sectionId,
			@RequestParam(value="chapterId",required=false) String chapterId){
		PaginatedResponse<GetQuestionBankResponse> response=questionBankService.getAllQuestionBank(courseId,sectionId,chapterId, pageNo, pageSize, sortOrder);
		return ResponseHelper.createResponse(new LMSResponse<PaginatedResponse>(), response,
				Translator.toLocale("questionbank.retrieved.success", null),
				Translator.toLocale("questionbank.retrieve.failed", null));
	}
	
	
	
	
	

}
