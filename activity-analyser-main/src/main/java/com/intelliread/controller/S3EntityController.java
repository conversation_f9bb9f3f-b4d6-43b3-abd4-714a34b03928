package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.ResponseHelper;
import com.intelliread.services.S3EntityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/v1/api/file/entity")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class S3EntityController {

	@Autowired
	private S3EntityService s3EntityService;

	@PostMapping("/upload-entity-json-s3")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> uploadEntityJsonS3(@RequestParam(value = "entityName") String entityName) {
		String response = s3EntityService.uploadEntityJsonS3(entityName);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("upload.entity.json.s3.success", null),
				Translator.toLocale("upload.entity.json.s3.failed", null));
	}
	
	@PostMapping("/upload-quizById-json-s3")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> uploadQuizByIdJsonS3(@RequestParam(value = "quizId") String quizId) {
		String response = s3EntityService.uploadQuizByIdJsonS3(quizId);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("upload.entity.json.s3.success", null),
				Translator.toLocale("upload.entity.json.s3.failed", null));
	}
	
	@PostMapping("/upload-entity-json-s3/quiz_show_correct_answer")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> uploadEntityJsonS3Quiz(@RequestParam(value = "entityName") String entityName,@RequestParam(value = "showCorrectAnswer") Boolean showCorrectAnswer) {
		String response = s3EntityService.uploadEntityJsonS3Quiz(entityName,showCorrectAnswer);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("upload.entity.json.s3.success", null),
				Translator.toLocale("upload.entity.json.s3.failed", null));
	}
}
