package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.PlanFinderRequestDto;
import com.intelliread.request.dto.PlansRequestDto;
import com.intelliread.response.dto.*;
import com.intelliread.response.models.GradesAndPlans;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.PaginatedResponse;
import com.intelliread.response.models.SubjectsSubtopicUnderGradeModel;
import com.intelliread.services.PlansService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/v1/api/master/plans")
public class PlansController {

	@Autowired
	private PlansService plansService;

	@Lazy
	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<PlansResponseDto> getPlansById(@PathVariable("id") String id) {
		PlansResponseDto response = plansService.getPlansById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<PlansResponseDto>(), response,
				Translator.toLocale("plans.fetch.success", null), Translator.toLocale("plans.fetch.failed", null));
	}

	@Lazy
	@GetMapping("/by-board-id")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<PlansResponseDto>> getAllPlansByBoardId(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "search", required = false) String search) {
		List<PlansResponseDto> response = plansService.getAllPlansByBoardId(boardId, search);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<PlansResponseDto>>(), response,
				Translator.toLocale("plans.fetch.all.success", null),
				Translator.toLocale("plans.fetch.all.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> deletePlansById(@PathVariable("id") String id) {
		Boolean response = plansService.deletePlansById(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("plans.deleted.success", null), Translator.toLocale("plans.deleted.failed", null));
	}

	@Lazy
	@GetMapping("/{id}/mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<PlanMappingsResponseDto> getAllPlanMappings(@PathVariable("id") String id) {
		PlanMappingsResponseDto response = plansService.getAllPlanMappings(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<PlansResponseDto>>(), response,
				Translator.toLocale("plans.fetch.all.mappings.success", null),
				Translator.toLocale("plans.fetch.all.mappings.failed", null));
	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	public LMSResponse<ConfirmationApiResponseDto> confirmationApiPlans(@RequestParam("planId") String planId,
			@RequestParam("operationType") String operationType) {
		ConfirmationApiResponseDto response = plansService.confirmationApiPlans(planId, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("plans.fetch.all.mappings.success", null),
				Translator.toLocale("plans.fetch.all.mappings.failed", null));
	}

	@Lazy
	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = plansService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/toggle-active")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> toggleActiveStatus(@RequestParam("id") String id,
			@RequestParam("active") boolean active) {
		Boolean response = plansService.toggleActiveStatus(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("active.status.toggle.success", null),
				Translator.toLocale("active.status.toggle.failed", null));
	}

	@Lazy
	@GetMapping("/subjects-from-plan-template")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<SubjectsResponseDto>> subjectListFromPlanTemplate(
			@RequestParam(value = "gradeId") String gradeId, @RequestParam(value = "planId") String planId) {
		List<SubjectsResponseDto> response = plansService.subjectListFromPlanTemplate(gradeId, planId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SubjectsResponseDto>>(), response,
				Translator.toLocale("plans.template.subject.fetch.success", null),
				Translator.toLocale("plans.template.subject.fetch.failed", null));
	}

	@Lazy
	@GetMapping("/page")
	@SuppressWarnings("unchecked")
	public LMSResponse<PaginatedResponse<PlansResponseDto>> getAllPlansByPagination(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<PlansResponseDto> response = plansService.getAllPlansByPagination(pageNumber, pageSize,
				sortBy, sortOrder, search, active);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<PaginatedResponse<PlansResponseDto>>(), response,
				Translator.toLocale("plans.fetch.all.success", null),
				Translator.toLocale("plans.fetch.all.failed", null));
	}

	@Lazy
	@GetMapping("/grades-from-plan/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<String>> assignedGradesFromPlan(@PathVariable("id") String id) {
		List<String> response = plansService.assignedGradesFromPlan(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("grades.fetch.all.success", null),
				Translator.toLocale("grades.fetch.all.failed", null));
	}

	// ***************** plan template v 2.0.0 **************//

	@Lazy
	@GetMapping("/applicable-boards")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<BoardsResponseDto>> getAllBoardFromSubjectMapping(
			@RequestParam(value = "search", required = false) String search) {
		List<BoardsResponseDto> response = plansService.getAllBoardFromSubjectMapping(search);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<BoardsResponseDto>>(), response,
				Translator.toLocale("subject.mapping.board.success", null),
				Translator.toLocale("subject.mapping.board.failed", null));
	}

	@Lazy
	@PostMapping()
	@SuppressWarnings("unchecked")
	public LMSResponse<PlansResponseDto> createOrUpdatePlanVersionTwo(@Valid @RequestBody PlansRequestDto request) {
		PlansResponseDto response = plansService.createOrUpdatePlanVersionTwo(request);
		return ResponseHelper.createResponse(new LMSResponse<PlansResponseDto>(), response,
				Translator.toLocale("plans.created.success", null), Translator.toLocale("plans.create.failed", null));
	}

	@Lazy
	@GetMapping("/for-other-admins")
	@SuppressWarnings("unchecked")
	public LMSResponse<PlansResponseDto> getPlanDetails(@RequestParam("id") String id) {
		PlansResponseDto response = plansService.getPlanDetails(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<PlansResponseDto>(), response,
				Translator.toLocale("plans.listed.for.other.admin.success", null),
				Translator.toLocale("plans.listed.for.other.admin.failed", null));
	}

	@Lazy
	@GetMapping("/template-screen")
	@SuppressWarnings("unchecked")
	public LMSResponse<PlanGradeBlueprintLevelResponseDto> planTemplateScreen(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "planId", required = false) String planId) {
		PlanGradeBlueprintLevelResponseDto response = plansService.planTemplateScreen(boardId, planId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<PlanGradeBlueprintLevelResponseDto>(), response,
				Translator.toLocale("plans.listed.template.screen.success", null),
				Translator.toLocale("plans.listed.template.screen.failed", null));
	}

	@Lazy
	@GetMapping("/purchased-features")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<PlanTemplateImplResponseDto>> purchasedPlanTemplate(
			@RequestParam(value = "planId", required = true) String planId,
			@RequestParam(value = "gradeIds", required = false) List<String> gradeIds,
			@RequestParam(value = "roleName", required = true) String roleName,
			@RequestParam(value = "subjectIds", required = false) List<String> subjectIds) {
		List<PlanTemplateImplResponseDto> response = plansService.purchasedPlanTemplate(planId, gradeIds, roleName,
				subjectIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<PlanTemplateImplResponseDto>>(), response,
				Translator.toLocale("plans.listed.template.screen.success", null),
				Translator.toLocale("plans.listed.template.screen.failed", null));
	}

	@Lazy
	@GetMapping("/subjects-by/purchased-feature")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<String>> listOfSubjectByPuchasedPlanFeature(
			@RequestParam(value = "planId", required = true) String planId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "columnName", required = true) String columnName) {
		List<String> response = plansService.listOfSubjectByPuchasedPlanFeature(planId, gradeId, columnName);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("plans.subject.list.success", null),
				Translator.toLocale("plans.subject.list.failed", null));
	}

	@Lazy
	@GetMapping("/subjects-by-template-feature")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<SubjectsMinResponseDto>> subjectsByPurchasedPlanTemplateFeature(
			@RequestParam(value = "planId", required = true) String planId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "columnName", required = true) String columnName) {
		List<SubjectsMinResponseDto> response = plansService.subjectsByPurchasedPlanTemplateFeature(planId, gradeId,
				columnName);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SubjectsMinResponseDto>>(), response,
				Translator.toLocale("plans.subject.list.success", null),
				Translator.toLocale("plans.subject.list.failed", null));
	}

	@Lazy
	@GetMapping("/get-by-ids")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<PlansResponseDto>> getAllPlansByIds(@RequestParam("ids") List<String> ids) {
		List<PlansResponseDto> response = plansService.getAllPlansByIds(ids);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<PlansResponseDto>>(), response,
				Translator.toLocale("plans.fetch.all.success", null),
				Translator.toLocale("plans.fetch.all.failed", null));
	}

	@Lazy
	@GetMapping("/by-feature-grade")
	@SuppressWarnings("unchecked")
	public LMSResponse<PaginatedResponse<PlansResponseDto>> allPlanByFeaturesOrGrades(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "lPlan", required = false) Boolean lPlan,
			@RequestParam(value = "teacherRM", required = false) Boolean teacherRM,
			@RequestParam(value = "rmLock", required = false) Boolean rmLock,
			@RequestParam(value = "activity", required = false) Boolean activity,
			@RequestParam(value = "assessment", required = false) Boolean assessment,
			@RequestParam(value = "wbD", required = false) Boolean wbD,
			@RequestParam(value = "wsD", required = false) Boolean wsD,
			@RequestParam(value = "news", required = false) Boolean news,
			@RequestParam(value = "booklet", required = false) Boolean booklet,
			@RequestParam(value = "studentRM", required = false) Boolean studentRM,
			@RequestParam(value = "gradeIds", required = false) List<String> gradeIds) {
		PaginatedResponse<PlansResponseDto> response = plansService.allPlanByFeaturesOrGrades(pageNumber, pageSize,
				lPlan, teacherRM, rmLock, activity, assessment, wbD, wsD, news, booklet, studentRM, gradeIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<PaginatedResponse<PlansResponseDto>>(), response,
				Translator.toLocale("plans.grade.feature.listing.success", null),
				Translator.toLocale("plans.grade.feature.listing.failed", null));
	}

	@Lazy
	@GetMapping("/feature-status/in-grades")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<GradesResponseDto>> planGradeFetureStatus(@RequestParam("planId") String planId) {
		List<GradesResponseDto> response = plansService.planGradeFetureStatus(planId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradesResponseDto>>(), response,
				Translator.toLocale("plans.grages.feature.status.success", null),
				Translator.toLocale("plans.grages.feature.status.failed", null));
	}

	@Lazy
	@PutMapping("/similar")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<String>> similarPlans(@RequestBody PlanFinderRequestDto request) {
		List<String> response = plansService.getTheSimilarPlans(request);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("plans.similar.success", null), Translator.toLocale("plans.similar.failed", null));
	}

	@Lazy
	@GetMapping("/subject-subtopic/listing")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<SubjectsSubtopicUnderGradeModel>> getTheSubjectsAndSimilarPlans(
			@RequestParam(value = "gradeIds", required = false) List<String> gradeIds,
			@RequestParam(value = "planId", required = true) String planId) {
		List<SubjectsSubtopicUnderGradeModel> response = plansService.findTheSubjectsAndSimilarPlans(gradeIds, planId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SubjectsSubtopicUnderGradeModel>>(), response,
				Translator.toLocale("plans.similar.success", null), Translator.toLocale("plans.similar.failed", null));
	}

	@Lazy
	@GetMapping("/simila/grade-wise")
	@SuppressWarnings("unchecked")
	public LMSResponse<GradesAndPlans> getSimilarPlansGradeWise(
			@RequestParam(value = "planId", required = true) String planId,
			@RequestParam(value = "coordinatorId", required = false) String coordinatorId) {
		GradesAndPlans response = plansService.getSimilarPlansGradeWise(planId, coordinatorId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<GradesAndPlans>(), response,
				Translator.toLocale("plans.similar.success", null), Translator.toLocale("plans.similar.failed", null));
	}

	@Lazy
	@GetMapping("/for/assign/assessments")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<String>> plansForAssignAssessmentPapers(@RequestParam("boardId") String boardId,
			@RequestParam("gradeId") String gradeId, @RequestParam("subjectId") String subjectId,
			@RequestParam("levelId") String levelId) {
		List<String> response = plansService.plansForAssignAssessmentPapers(boardId, gradeId, subjectId, levelId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("plans.for.assign.assessment.success", null),
				Translator.toLocale("plans.for.assign.assessment.failed", null));
	}

	@Lazy
	@GetMapping("/assign/blueprint-levels")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<String>> assignedBlueprintLevels(@RequestParam("planId") String planId) {
		List<String> response = plansService.assignedBlueprintLevels(planId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("plan.bleuprint.level.success", null),
				Translator.toLocale("plan.bleuprint.level.failed", null));
	}

	@Lazy
	@GetMapping("/csv/for-student")
	@SuppressWarnings("unchecked")
	public LMSResponse<Map<String, List<String>>> getAllPlanAndItsGrades() {
		Map<String, List<String>> response = plansService.getAllPlanAndItsGrades();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Map<String, List<String>>>(), response,
				Translator.toLocale("plan.grade.success", null), Translator.toLocale("plan.grade.failed", null));
	}
}
