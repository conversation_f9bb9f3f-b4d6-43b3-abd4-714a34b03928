package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.CourseAssignmentsRequestDto;
import com.intelliread.response.dto.CourseAssignmentsResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.CourseAssignmentsService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping(value = "/v1/api/master/courseAssignments")
public class CourseAssignmentsController {
	
	@Autowired
	CourseAssignmentsService courseAssignmentsService;
	
	@PostMapping()
	@SuppressWarnings("all")
	public LMSResponse<CourseAssignmentsResponseDto> saveCourseAssignment(@Valid @RequestBody CourseAssignmentsRequestDto dto){
		CourseAssignmentsResponseDto response=courseAssignmentsService.saveCourseAssignment(dto);
		return ResponseHelper.createResponse(new LMSResponse<CourseAssignmentsResponseDto>(), response,
				Translator.toLocale("courseassignments.created.success", null),
				Translator.toLocale("courseassignments.create.failed", null));
	}
	
	
	@PutMapping("/complete/{status}/{courseId}/{assignedUserId}")
	@SuppressWarnings("all")
	public LMSResponse<CourseAssignmentsResponseDto> updateCourseAssignment(@PathVariable("status") String status,@PathVariable("courseId") String courseId,@PathVariable("assignedUserId") String assignedUserId){
		CourseAssignmentsResponseDto response=courseAssignmentsService.updateCourseAssignment(status,courseId,assignedUserId);
		return ResponseHelper.createResponse(new LMSResponse<CourseAssignmentsResponseDto>(), response,
				Translator.toLocale("courseassignments.updated.success", null),
				Translator.toLocale("courseassignments.update.failed", null));
	}
	
	@GetMapping("/{assignedUserId}")
	@SuppressWarnings("all")
	public LMSResponse<CourseAssignmentsResponseDto> getCourseAssigned(@PathVariable("assignedUserId") String assignedUserId,@RequestParam(value="status",required=false) String status){
		CourseAssignmentsResponseDto response=courseAssignmentsService.getCourseAssigned(assignedUserId,status);
		return ResponseHelper.createResponse(new LMSResponse<CourseAssignmentsResponseDto>(), response,
				Translator.toLocale("courseassignments.retrieved.success", null),
				Translator.toLocale("courseassignments.retrieved.failed", null));
	}
	
	
//	@GetMapping("/getPendingVolunteerRequest/{courseAssignmentId}")
//	@SuppressWarnings("all")
//	public LMSResponse<GetCoursesResponse> getPendingVolunteerRequest(@PathVariable("courseAssignmentId") String courseAssignmentId){
//		GetCoursesResponse response=courseAssignmentsService.getPendingVolunteerRequest(courseAssignmentId);
//		return ResponseHelper.createResponse(new LMSResponse<GetCoursesResponse>(), response,
//				Translator.toLocale("courseassignments.retrieved.success", null),
//				Translator.toLocale("courseassignments.retrieved.failed", null));
//	}
//	
//	
//	@GetMapping("/getPendingNominationRequest/{courseAssignmentId}")
//	@SuppressWarnings("all")
//	public LMSResponse<GetCoursesResponse> getPendingNominationRequest(@PathVariable("courseAssignmentId") String courseAssignmentId){
//		GetCoursesResponse response=courseAssignmentsService.getPendingNominationRequest(courseAssignmentId);
//		return ResponseHelper.createResponse(new LMSResponse<GetCoursesResponse>(), response,
//				Translator.toLocale("courseassignments.retrieved.success", null),
//				Translator.toLocale("courseassignments.retrieved.failed", null));
//	}
//	
//	@GetMapping("/getAssignees/{courseAssignmentId}/{courseId}/{currentStatus}")
//	@SuppressWarnings("all")
//	public LMSResponse<AssignedUserIdDto> getAssignees(@PathVariable("courseAssignmentId") String courseAssignmentId,@PathVariable("courseId") String courseId,@PathVariable("currentStatus") String currentStatus){
//		AssignedUserIdDto response=courseAssignmentsService.getAssignees(courseAssignmentId,courseId,currentStatus);
//		return ResponseHelper.createResponse(new LMSResponse<AssignedUserIdDto>(), response,
//				Translator.toLocale("courseassignments.retrieved.success", null),
//				Translator.toLocale("courseassignments.retrieved.failed", null));
//	}
//	
//	
//	@PostMapping("/assignCourse")
//	@SuppressWarnings("all")
//	public LMSResponse<CourseAssignmentsResponseDto> assignCourse(@Valid @RequestBody CourseAssignmentsRequestDto dto){
//		CourseAssignmentsResponseDto response=courseAssignmentsService.assignCourse(dto);
//		return ResponseHelper.createResponse(new LMSResponse<CourseAssignmentsResponseDto>(), response,
//				Translator.toLocale("courseassignments.created.success", null),
//				Translator.toLocale("courseassignments.create.failed", null));
//	}
//	
//	
//	@PostMapping("/nominateCourse/{courseId}/{nominatedBy}")
//	@SuppressWarnings("all")
//	public LMSResponse<AssignCourseDto> nominateCourse(@PathVariable("courseId") String courseId,@PathVariable("nominatedBy") String nominatedBy,@Valid @RequestBody CourseAssignmentsRequestDto dto){
//		AssignCourseDto response=courseAssignmentsService.nominateCourse(courseId,nominatedBy,dto);
//		if(response.getAssignedUserId()=="" && response.getCourses()=="") {
//			return ResponseHelper.createResponse(new LMSResponse<AssignCourseDto>(), response,
//					Translator.toLocale("courseassignments.already.created", null),
//					Translator.toLocale("courseassignments.create.failed", null));
//		}
//		return ResponseHelper.createResponse(new LMSResponse<AssignCourseDto>(), response,
//				Translator.toLocale("nominatecourse.approved.success", null),
//				Translator.toLocale("nominatecourse.approved.failed", null));
//	}
//	
//	
//	@PostMapping("/volunteerCourse/{courseId}")
//	@SuppressWarnings("all")
//	public LMSResponse<AssignCourseDto> volunteerCourse(@PathVariable("courseId") String courseId,@Valid @RequestBody CourseAssignmentsRequestDto dto){
//		AssignCourseDto response=courseAssignmentsService.volunteerCourse(courseId,dto);
//		if(response.getAssignedUserId()=="" && response.getCourses()=="") {
//			return ResponseHelper.createResponse(new LMSResponse<AssignCourseDto>(), response,
//					Translator.toLocale("courseassignments.already.created", null),
//					Translator.toLocale("courseassignments.create.failed", null));
//		}
//		return ResponseHelper.createResponse(new LMSResponse<AssignCourseDto>(), response,
//				Translator.toLocale("volunteercourse.created.success", null),
//				Translator.toLocale("volunteercourse.create.failed", null));
//	}
//	
//	
//	@PostMapping("/acceptRejectVolunteer/{courseAssignmentId}/{courseId}/{requestStatus}")
//	@SuppressWarnings("all")
//	public LMSResponse<AcceptRejectCourseDto> acceptRejectVolunteer(@PathVariable("courseAssignmentId") String courseAssignmentId,@PathVariable("courseId") String courseId,@PathVariable("requestStatus") String requestStatus){
//		AcceptRejectCourseDto response=courseAssignmentsService.acceptRejectVolunteer(courseAssignmentId,courseId,requestStatus);
//		if(response.getCourseAssignmentId()=="" && response.getCourses()=="" && response.getCourseId()=="") {
//			return ResponseHelper.createResponse(new LMSResponse<AssignCourseDto>(), response,
//					Translator.toLocale("courses.already.approved", null),
//					Translator.toLocale("courseassignments.create.failed", null));
//		}
//		return ResponseHelper.createResponse(new LMSResponse<AcceptRejectCourseDto>(), response,
//				Translator.toLocale("volunteercourse.approved.success", null),
//				Translator.toLocale("volunteercourse.approved.failed", null));
//	}
//	
//	@PostMapping("/acceptRejectNomination/{courseAssignmentId}")
//	@SuppressWarnings("all")
//	public LMSResponse<AcceptRejectCourseDto> acceptRejectNomination(@PathVariable("courseAssignmentId") String courseAssignmentId,@PathVariable("courseId") String courseId,@PathVariable("nominationStatus") String nominationStatus){
//		AcceptRejectCourseDto response=courseAssignmentsService.acceptRejectNomination(courseAssignmentId,courseId,nominationStatus);
//		if(response.getCourseAssignmentId()=="" && response.getCourses()=="" && response.getCourseId()=="") {
//			return ResponseHelper.createResponse(new LMSResponse<AssignCourseDto>(), response,
//					Translator.toLocale("courses.already.approved", null),
//					Translator.toLocale("courseassignments.create.failed", null));
//		}
//		return ResponseHelper.createResponse(new LMSResponse<AcceptRejectCourseDto>(), response,
//				Translator.toLocale("nominatecourse.approved.success", null),
//				Translator.toLocale("nominatecourse.approved.failed", null));
//	}
//	
//	
//	@PostMapping("/approveAllCourses/{courseAssignmentId}")
//	@SuppressWarnings("all")
//	public LMSResponse<ApproveAllCourses> approveAllCourses(@PathVariable("courseAssignmentId") String courseAssignmentId){
//		ApproveAllCourses response=courseAssignmentsService.approveAllCourses(courseAssignmentId);
//		return ResponseHelper.createResponse(new LMSResponse<ApproveAllCourses>(), response,
//				Translator.toLocale("courseassignments.courses.approved", null),
//				Translator.toLocale("courseassignments.approved.failed", null));
//	}

}
