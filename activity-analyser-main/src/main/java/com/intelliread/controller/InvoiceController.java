package com.intelliread.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.intelliread.component.Translator;
import com.intelliread.entity.master.PaymentTransaction;
import com.intelliread.repository.master.PaymentTransactionRepository;
import com.intelliread.request.dto.SendPaymentLinkRequestDto;
import com.intelliread.response.dto.DeleteCourse;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.InvoiceService;
import com.intelliread.services.PaymentUserService;
import com.intelliread.utilities.ResponseHelper;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/v1/api/invoices")
public class InvoiceController {

    @Autowired
    private InvoiceService invoiceService;

    @PostMapping("/send-proforma-invoice")
    @SuppressWarnings("all")
    public LMSResponse<JsonNode> sendPaymentLink(@Valid @RequestBody SendPaymentLinkRequestDto requestDto) throws IOException {
        JsonNode invoice = invoiceService.sendProformaInvoiceByUserAndTransactionId(requestDto);

        return ResponseHelper.createResponse(new LMSResponse<JsonNode>(), invoice,
                Translator.toLocale("invoice.proforma.response.success", null),
                Translator.toLocale("invoice.proforma.response.failed", null));
    }
}
