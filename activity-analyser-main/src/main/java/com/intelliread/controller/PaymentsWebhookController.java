package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.razorpay.PaymentSuccessDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.PaymentsWebhookService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/v1/api/webhooks")
@SuppressWarnings("all")
public class PaymentsWebhookController {

    @Autowired
    private PaymentsWebhookService paymentsWebhookService;

    @GetMapping("/payment-success")
    public LMSResponse<?> handlePaymentCompleteCallback(@RequestParam("razorpay_payment_id") String razorpayPaymentId,
                                                     @RequestParam("razorpay_payment_link_id") String razorpayPaymentLinkId,
                                                     @RequestParam("razorpay_payment_link_reference_id") String razorpayPaymentLinkReferenceId,
                                                     @RequestParam("razorpay_payment_link_status") String razorpayPaymentLinkStatus,
                                                     @RequestParam("razorpay_signature") String razorpaySignature) {
        PaymentSuccessDto paymentSuccessDTO = new PaymentSuccessDto();
        paymentSuccessDTO.setRazorpayPaymentId(razorpayPaymentId);
        paymentSuccessDTO.setRazorpayPaymentLinkId(razorpayPaymentLinkId);
        paymentSuccessDTO.setRazorpayPaymentLinkReferenceId(razorpayPaymentLinkReferenceId);
        paymentSuccessDTO.setRazorpayPaymentLinkStatus(razorpayPaymentLinkStatus);
        paymentSuccessDTO.setRazorpaySignature(razorpaySignature);

        Map<String, String> response = new HashMap<>();
        response.put("status", razorpayPaymentLinkStatus);

        try {
            paymentsWebhookService.handlePaymentSuccessCallback(paymentSuccessDTO);
            return ResponseHelper.createResponse(new LMSResponse<>(), response,
                    Translator.toLocale("payment.success.callback.complete", null), null);
        } catch (Exception e) {
            return ResponseHelper.createResponse(new LMSResponse<>(), null,
                    null, e.getMessage());
        }
    }
}
