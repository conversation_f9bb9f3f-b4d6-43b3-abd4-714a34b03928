package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.CreateLevelRequest;
import com.intelliread.response.dto.LevelResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.LevelService;
import com.intelliread.utilities.ResponseHelper;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/api/levels")
public class LevelsController {

    @Autowired
    private LevelService levelService;

    @SuppressWarnings("unchecked")
    @GetMapping()
    public LMSResponse<List<LevelResponseDto>> getLevels() {
        List<LevelResponseDto> levels = levelService.getAllLevels();
        return ResponseHelper.createResponse(new LMSResponse<List<LevelResponseDto>>(), levels,
                Translator.toLocale("level.response.list.success", null),
                Translator.toLocale("level.response.list.failed", null));
    }

    @SuppressWarnings("unchecked")
    @GetMapping("/{id}")
    public LMSResponse<LevelResponseDto> getLevelById(@PathVariable String id) {
        LevelResponseDto level = levelService.getLevelById(id);
        return ResponseHelper.createResponse(new LMSResponse<LevelResponseDto>(), level,
                Translator.toLocale("level.response.success", null),
                Translator.toLocale("level.response.failed", null));
    }
    @SuppressWarnings("unchecked")
    @GetMapping("/{name}")
    public LMSResponse<LevelResponseDto> getLevelByName(String name) {
        LevelResponseDto level = levelService.getLevelByName(name);
        return ResponseHelper.createResponse(new LMSResponse<LevelResponseDto>(), level,
                Translator.toLocale("level.response.success", null),
                Translator.toLocale("level.response.failed", null));
    }

    @SuppressWarnings("unchecked")
    @PostMapping()
    public LMSResponse<LevelResponseDto> createLevel(@Valid @RequestBody CreateLevelRequest level) {
        LevelResponseDto updatedLevel = levelService.createLevel(level);
        return ResponseHelper.createResponse(new LMSResponse<LevelResponseDto>(), updatedLevel,
                Translator.toLocale("level.response.create.success", null),
                Translator.toLocale("level.response.create.failed", null));
    }

    @SuppressWarnings("unchecked")
    @PutMapping("/{id}")
    public LMSResponse<LevelResponseDto> updateLevel(@PathVariable String id, @Valid @RequestBody CreateLevelRequest level) {
        LevelResponseDto updatedLevel = levelService.updateLevel(id, level);
        return ResponseHelper.createResponse(new LMSResponse<LevelResponseDto>(), updatedLevel,
                Translator.toLocale("level.response.update.success", null),
                Translator.toLocale("level.response.update.failed", null));
    }

    @SuppressWarnings("unchecked")
    @DeleteMapping("/{id}")
    public LMSResponse<LevelResponseDto> deleteLevel(@PathVariable String id) {
        LevelResponseDto deletedLevel = levelService.deleteLevel(id);
        return ResponseHelper.createResponse(new LMSResponse<LevelResponseDto>(), deletedLevel,
                Translator.toLocale("level.response.delete.success", null),
                Translator.toLocale("level.response.delete.failed", null));
    }
}
