package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.RolesRequestDto;
import com.intelliread.response.dto.ConfirmationApiResponseDto;
import com.intelliread.response.dto.RoleMappingResponse;
import com.intelliread.response.dto.RolesResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.PaginatedResponse;
import com.intelliread.services.RolesService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

@RestController
@RequestMapping(value = "/v1/api/master/roles")
public class RolesController {

	@Autowired
	private RolesService rolesService;

	@PostMapping()
	@SuppressWarnings("unchecked")
	public LMSResponse<RolesResponseDto> createRoles(@Valid @RequestBody RolesRequestDto request) {
		RolesResponseDto response = rolesService.createRoles(request);
		return ResponseHelper.createResponse(new LMSResponse<RolesResponseDto>(), response,
				Translator.toLocale("roles.created.success", null), Translator.toLocale("roles.create.failed", null));
	}

	@PutMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<RolesResponseDto> updateRoles(@PathVariable("id") String id,
			@Valid @RequestBody RolesRequestDto request) {
		RolesResponseDto response = rolesService.updateRoles(id, request);
		return ResponseHelper.createResponse(new LMSResponse<RolesResponseDto>(), response,
				Translator.toLocale("roles.update.success", null), Translator.toLocale("roles.update.failed", null));
	}

	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<RolesResponseDto> getRolesById(@PathVariable("id") String id) {
		RolesResponseDto response = rolesService.getRolesById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<RolesResponseDto>(), response,
				Translator.toLocale("roles.fetch.success", null), Translator.toLocale("roles.fetch.failed", null));
	}

	@GetMapping("/all")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<RolesResponseDto>> getAllRoles() {
		List<RolesResponseDto> response = rolesService.getAllRoles();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<RolesResponseDto>>(), response,
				Translator.toLocale("roles.fetch.all.success", null),
				Translator.toLocale("roles.fetch.all.failed", null));
	}

	@GetMapping("/all/{ids}")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<RolesResponseDto>> getRolesByIds(@PathVariable("ids") List<String> ids) {
		List<RolesResponseDto> response = rolesService.getRolesByIds(ids);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<RolesResponseDto>>(), response,
				Translator.toLocale("roles.fetch.all.success", null),
				Translator.toLocale("roles.fetch.all.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> deleteRolesById(@PathVariable("id") String id) {
		Boolean response = rolesService.deleteRolesById(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("roles.deleted.success", null), Translator.toLocale("roles.deleted.failed", null));
	}

	/**
	 * API mainly using for the user-service
	 * 
	 * @param key
	 * @return
	 */
	@GetMapping("/by-key")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getOnlyIdByRole(@RequestParam("key") String key) {
		String response = rolesService.getOnlyIdByRole(key);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("roles.fetch.success", null), Translator.toLocale("roles.fetch.failed", null));
	}

	@PutMapping("/toggle-active")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> toggleActiveStatus(@RequestParam("id") String id,
			@RequestParam("active") boolean active) {
		Boolean response = rolesService.toggleActiveStatus(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("active.status.toggle.success", null),
				Translator.toLocale("active.status.toggle.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = rolesService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/{id}/mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<RoleMappingResponse> getAllMappingsAssociatedWithRoles(@PathVariable("id") String id) {
		RoleMappingResponse response = rolesService.getAllMappingsAssociatedWithRoles(id);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("roles.fetch.all.mappings.success", null),
				Translator.toLocale("roles.fetch.all.mappings.failed", null));
	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	public LMSResponse<ConfirmationApiResponseDto> confirmationAPI(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		ConfirmationApiResponseDto response = rolesService.confirmationApi(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("confirmation.api.success", null),
				Translator.toLocale("confirmation.api.failed", null));
	}

	@Lazy
	@GetMapping("/page")
	@SuppressWarnings("unchecked")
	public LMSResponse<PaginatedResponse<RolesResponseDto>> getAllRoleByPagination(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<RolesResponseDto> response = rolesService.getAllRoleByPagination(pageNumber, pageSize, sortBy,
				sortOrder, search, active);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<PaginatedResponse<RolesResponseDto>>(), response,
				Translator.toLocale("roles.fetch.all.success", null),
				Translator.toLocale("roles.fetch.all.failed", null));
	}

	@Lazy
	@GetMapping("/admin-roles")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<RolesResponseDto>> getAllAdminRoles() {
		List<RolesResponseDto> response = rolesService.getAllAdminRoles();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<RolesResponseDto>>(), response,
				Translator.toLocale("roles.fetch.all.success", null),
				Translator.toLocale("roles.fetch.all.failed", null));
	}

	@Lazy
	@GetMapping("/academic-persona")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<RolesResponseDto>> getAllTeacherAndStudentsRoles() {
		List<RolesResponseDto> response = rolesService.getAllTeacherAndStudentsRoles();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<RolesResponseDto>>(), response,
				Translator.toLocale("roles.fetch.all.success", null),
				Translator.toLocale("roles.fetch.all.failed", null));
	}
}
