package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.SectionsRequestDto;
import com.intelliread.response.dto.SectionsResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.SectionsService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(value = "/v1/api/master/sections")
public class SectionsController {

	@Autowired
	private SectionsService sectionsService;

	@PostMapping()
	@SuppressWarnings("unchecked")
	public LMSResponse<SectionsResponseDto> createSections(@Valid @RequestBody SectionsRequestDto request) {
		SectionsResponseDto response = sectionsService.createSections(request);
		return ResponseHelper.createResponse(new LMSResponse<SectionsResponseDto>(), response,
				Translator.toLocale("sections.created.success", null),
				Translator.toLocale("sections.create.failed", null));
	}

	@PutMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<SectionsResponseDto> updateSections(@PathVariable("id") String id,
			@Valid @RequestBody SectionsRequestDto request) {
		SectionsResponseDto response = sectionsService.updateSections(id, request);
		return ResponseHelper.createResponse(new LMSResponse<SectionsResponseDto>(), response,
				Translator.toLocale("sections.update.success", null),
				Translator.toLocale("sections.update.failed", null));
	}

	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<SectionsResponseDto> getSectionsById(@PathVariable("id") String id) {
		SectionsResponseDto response = sectionsService.getSectionsById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<SectionsResponseDto>(), response,
				Translator.toLocale("sections.fetch.success", null),
				Translator.toLocale("sections.fetch.failed", null));
	}

	@GetMapping("/all")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<SectionsResponseDto>> getAllSections(
			@RequestParam(value = "search", required = false) String search) {
		List<SectionsResponseDto> response = sectionsService.getAllSections(search);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SectionsResponseDto>>(), response,
				Translator.toLocale("sections.fetch.all.success", null),
				Translator.toLocale("sections.fetch.all.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> deleteSectionsById(@PathVariable("id") String id) {
		Boolean response = sectionsService.deleteSectionsById(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("sections.deleted.success", null),
				Translator.toLocale("sections.deleted.failed", null));
	}

	@GetMapping("/all-by-ids")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<SectionsResponseDto>> getAllSectionsByIds(@RequestParam("ids") List<String> ids) {
		List<SectionsResponseDto> response = sectionsService.getAllSectionsByIds(ids);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SectionsResponseDto>>(), response,
				Translator.toLocale("sections.fetch.all.success", null),
				Translator.toLocale("sections.fetch.all.failed", null));
	}

	@GetMapping("/is-exist")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> existSectionsByIds(@RequestParam("ids") List<String> ids) {
		Boolean response = sectionsService.existSectionsByIds(ids);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("sections.exist.success", null),
				Translator.toLocale("sections.exist.failed", null));
	}

	@GetMapping("/{sectionId}/mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getMappedData(@PathVariable("sectionId") String sectionId) {
		String response = sectionsService.getCountOfStudentAndSectionBySectionId(sectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("sections.mapped.data.success", null),
				Translator.toLocale("sections.mapped.data.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = sectionsService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}
}
