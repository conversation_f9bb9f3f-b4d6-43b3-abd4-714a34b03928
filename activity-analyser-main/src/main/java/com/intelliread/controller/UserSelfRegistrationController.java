package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.feign.master.MastersFeignClient;
import com.intelliread.repository.users.StudentsRepository;
import com.intelliread.repository.users.TeacherRepository;
import com.intelliread.request.dto.SelfRegistrationStudentRequest;
import com.intelliread.response.dto.UsersResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.UserSelfRegistrationService;
import com.intelliread.utilities.ResponseHelper;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/api/user/self-register")
public class UserSelfRegistrationController {

    private static final Logger log = LoggerFactory.getLogger(UserSelfRegistrationController.class);
    @Autowired
    private UserSelfRegistrationService userSelfRegistrationService;

    @Autowired
    TeacherRepository teacherRepository;

    @Autowired
    StudentsRepository studentRepository;

    @Autowired
    MastersFeignClient mastersFeignClient;

    @PostMapping("/student")
    @SuppressWarnings("unchecked")
    public LMSResponse<UsersResponseDto> onboardStudent(@Valid @RequestBody SelfRegistrationStudentRequest request) {
        UsersResponseDto response = userSelfRegistrationService.selfOnboardStudent(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.created.successfully", null),
                Translator.toLocale("user.create.failed", null));
    }
}
