package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.feign.master.GradesResponseDto;
import com.intelliread.feign.master.SectionsResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.request.dto.GradeSectionMapRequestDto;
import com.intelliread.request.dto.GradeSectionPutRequestDto;
import com.intelliread.request.dto.ToggleGradeSectionRequestDto;
import com.intelliread.response.dto.*;
import com.intelliread.services.GradeSectionMappingService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(value = "/v1/api/user/grade-section")
public class GradeSectionMappingController {

	@Autowired
	private GradeSectionMappingService gradeSectionMappingService;

	@GetMapping("/section-data")
	@SuppressWarnings("unchecked")
	@Lazy
	public LMSResponse<List<SectionDataResponseDto>> getAllSectionData() {
		List<SectionDataResponseDto> response = gradeSectionMappingService.getAllSectionData();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SectionDataResponseDto>>(), response,
				Translator.toLocale("section.data.get.all.success", null),
				Translator.toLocale("section.data.get.all.failed", null));
	}

	@PostMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	public LMSResponse<List<GradeSectionMapResponseDto>> createGradeSectionMapping(
			@Valid @RequestBody GradeSectionMapRequestDto request) {
		List<GradeSectionMapResponseDto> response = gradeSectionMappingService.createGradeSectionMapping(request);
		return ResponseHelper.createResponse(new LMSResponse<List<GradeSectionMapResponseDto>>(), response,
				Translator.toLocale("gs.mapping.create.success", null),
				Translator.toLocale("gs.mapping.create.failed", null));
	}

	@GetMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	public LMSResponse<List<GradeSectionGetResponseDto>> getAllMappingBySchoolAndBranch(
			@RequestParam("schoolId") String schoolId, @RequestParam("branchId") String branchId) {
		List<GradeSectionGetResponseDto> response = gradeSectionMappingService.getAllMappingBySchoolAndBranch(schoolId,
				branchId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradeSectionGetResponseDto>>(), response,
				Translator.toLocale("gs.mapping.create.success", null),
				Translator.toLocale("gs.mapping.create.failed", null));
	}

	@PutMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	public LMSResponse<GradeSectionMapResponseDto> updateGradeSectionsMapping(
			@Valid @RequestBody GradeSectionPutRequestDto request) {
		GradeSectionMapResponseDto response = gradeSectionMappingService.updateGradeSectionsMapping(request);
		return ResponseHelper.createResponse(new LMSResponse<GradeSectionMapResponseDto>(), response,
				Translator.toLocale("gs.mapping.update.success", null),
				Translator.toLocale("gs.mapping.update.failed", null));
	}

	@SuppressWarnings("unchecked")
	@Lazy
	@DeleteMapping()
	public LMSResponse<Boolean> deleteActiveFieldByGradeIdBranchIdAndSchoolId(
			@Valid @RequestBody ToggleGradeSectionRequestDto request) {
		boolean response = gradeSectionMappingService.deleteActiveFieldByGradeIdBranchIdAndSchoolId(request);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("gs.mapping.delete.success", null),
				Translator.toLocale("gs.mapping.delete.failed", null));
	}

	@SuppressWarnings("unchecked")
	@Lazy
	@PutMapping("/toggle/active")
	public LMSResponse<Boolean> updateActiveFieldByGradeIdBranchIdAndSchoolId(
			@Valid @RequestBody ToggleGradeSectionRequestDto request) {
		boolean response = gradeSectionMappingService.updateActiveFieldByGradeIdBranchIdAndSchoolId(request);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@GetMapping("/{gradeId}/grade-mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> checkGradeMappedData(@PathVariable("gradeId") String gradeId) {
		Boolean response = gradeSectionMappingService.getCountOfGradeSectionsByGradeId(gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response,
				Translator.toLocale("gs.mapped.with.grade.success", null),
				Translator.toLocale("gs.mapped.with.grade.failed", null));
	}

	@GetMapping("/{sectionId}/section-mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> checkSectionMappedData(@PathVariable("sectionId") String sectionId) {
		Boolean response = gradeSectionMappingService.getCountOfGradeSectionsBySectionId(sectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("gs.mapped.with.section.success", null),
				Translator.toLocale("gs.mapped.with.section.failed", null));
	}

	@GetMapping("/grades")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<GradesResponseDto>> getAllGradesBySchoolAndBranch(@RequestParam("branchId") String branchId,
			@RequestParam("schoolId") String schoolId) {
		List<GradesResponseDto> response = gradeSectionMappingService.getAllGradesBySchoolAndBranch(branchId, schoolId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradesResponseDto>>(), response,
				Translator.toLocale("gs.mapped.with.grade.success", null),
				Translator.toLocale("gs.mapped.with.grade.failed", null));
	}

	@GetMapping("/sections")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<SectionsResponseDto>> getAllSectionByGradesSchoolAndBranch(
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "schoolId", required = true) String schoolId) {
		List<SectionsResponseDto> response = gradeSectionMappingService.getAllSectionByGradesSchoolAndBranch(search,
				gradeId, branchId, schoolId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SectionsResponseDto>>(), response,
				Translator.toLocale("gs.mapped.with.section.success", null),
				Translator.toLocale("gs.mapped.with.section.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = gradeSectionMappingService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/all")
	@SuppressWarnings("unchecked")
	@Lazy
	public LMSResponse<List<GradeSectionMappingResponseDto>> getMappingsBySchoolIdAndBranchId(
			@RequestParam("schoolId") String schoolId, @RequestParam("branchId") String branchId) {
		List<GradeSectionMappingResponseDto> response = gradeSectionMappingService
				.getMappingsBySchoolIdAndBranchId(schoolId, branchId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradeSectionMappingResponseDto>>(), response,
				Translator.toLocale("gs.mapping.create.success", null),
				Translator.toLocale("gs.mapping.create.failed", null));
	}

	@SuppressWarnings("unchecked")
	@Lazy
	@DeleteMapping("/{id}")
	public LMSResponse<Boolean> deleteById(@PathVariable("id") String id) {
		boolean response = gradeSectionMappingService.deleteById(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("gs.mapping.delete.success", null),
				Translator.toLocale("gs.mapping.delete.failed", null));
	}

	@SuppressWarnings("unchecked")
	@Lazy
	@GetMapping("/toggle-active/{id}")
	public LMSResponse<Boolean> toggleActiveStatusById(@PathVariable("id") String id,
			@RequestParam(value = "active") boolean active) {
		boolean response = gradeSectionMappingService.toggleActiveStatusById(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@GetMapping("/confirm/{mappingId}")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> checkMappedData(@PathVariable("mappingId") String mappingId) {
		Boolean response = gradeSectionMappingService.existsByIdAndDeleted(mappingId, false);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("gs.mapped.with.section.success", null),
				Translator.toLocale("gs.mapped.with.section.failed", null));
	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	public LMSResponse<ConfirmationApiResponseDto> confirmationApi(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		ConfirmationApiResponseDto response = gradeSectionMappingService.confirmationApi(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("confirmation.api.success", null),
				Translator.toLocale("confirmation.api.failed", null));
	}

	@Lazy
	@GetMapping("/checking-mapping-for-grade/{gradeId}")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> checkTheGradeHasMapping(@PathVariable("gradeId") String gradeId) {
		boolean response = gradeSectionMappingService.checkTheGradeHasMapping(gradeId);
		String message = response ? Translator.toLocale("mapping.found", null)
				: Translator.toLocale("mapping.not.found", null);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response, message,
				Translator.toLocale("mapping.api.failed", null));
	}
	
	@GetMapping("/grade-related/sections")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<String>> getAllSectionsForPrincipal(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId) {
		List<String> response = gradeSectionMappingService.getAllSectionsForPrincipal(schoolId, branchId, gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("gs.mapped.with.section.success", null),
				Translator.toLocale("gs.mapped.with.section.failed", null));
	}

	
		@GetMapping("/getMappedDataForGradeId/{gradeId}")
		@SuppressWarnings("unchecked")
		public LMSResponse<List<String>> getMappedDataForGradeId(@PathVariable("gradeId") String gradeId) {
			List<String> response = gradeSectionMappingService.getSectionByGradeId(gradeId);
			return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
					Translator.toLocale("gs.mapped.with.grade.success", null),
					Translator.toLocale("gs.mapped.with.grade.failed", null));
		}
}
