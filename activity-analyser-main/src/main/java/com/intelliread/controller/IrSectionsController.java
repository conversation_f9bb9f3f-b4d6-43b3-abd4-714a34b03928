package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.IrSectionsRequestDto;
import com.intelliread.response.dto.GetIrSectionsList;
import com.intelliread.response.dto.IrSectionsResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.IrSectionsService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping(value = "/v1/api/irsections")
public class IrSectionsController {
	
	@Autowired
	IrSectionsService irSectionsService;
	
	
	@PostMapping()
	@SuppressWarnings("all")
	public LMSResponse<IrSectionsResponseDto> createIrSections(@Valid @RequestBody IrSectionsRequestDto dto){
		IrSectionsResponseDto response= irSectionsService.createIrSections(dto);
		return  ResponseHelper.createResponse(new LMSResponse<IrSectionsResponseDto>(), response,
				Translator.toLocale("irsections.created.success", null),
				Translator.toLocale("irsections.create.failed", null));
	}
	
	@GetMapping()
	@SuppressWarnings("all")
	public LMSResponse<GetIrSectionsList> getIrSections(){
		GetIrSectionsList response= irSectionsService.getIrSections();
		return  ResponseHelper.createResponse(new LMSResponse<GetIrSectionsList>(), response,
				Translator.toLocale("irsections.retrieved.success", null),
				Translator.toLocale("irsections.retrieve.failed", null));

	}
	

}
