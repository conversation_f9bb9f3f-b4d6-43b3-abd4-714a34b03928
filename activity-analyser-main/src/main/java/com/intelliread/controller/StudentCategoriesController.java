package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.StudentCategoriesRequestDto;
import com.intelliread.response.dto.NameCommonResponseDto;
import com.intelliread.response.dto.StudentCategoriesResponseDto;
import com.intelliread.response.dto.StudentCategoryMappingResponse;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.StudentCategoriesService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(value = "/v1/api/master/student-categories")
public class StudentCategoriesController {

	@Autowired
	private StudentCategoriesService studentCategoriesService;

	@PostMapping()
	@SuppressWarnings("unchecked")
	public LMSResponse<StudentCategoriesResponseDto> createStudentCategories(
			@Valid @RequestBody StudentCategoriesRequestDto request) {
		StudentCategoriesResponseDto response = studentCategoriesService.createStudentCategories(request);
		return ResponseHelper.createResponse(new LMSResponse<StudentCategoriesResponseDto>(), response,
				Translator.toLocale("student.categories.created.success", null),
				Translator.toLocale("student.categories.create.failed", null));
	}

	@PutMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<StudentCategoriesResponseDto> updateStudentCategories(@PathVariable("id") String id,
			@Valid @RequestBody StudentCategoriesRequestDto request) {
		StudentCategoriesResponseDto response = studentCategoriesService.updateStudentCategories(id, request);
		return ResponseHelper.createResponse(new LMSResponse<StudentCategoriesResponseDto>(), response,
				Translator.toLocale("student.categories.update.success", null),
				Translator.toLocale("student.categories.update.failed", null));
	}

	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<StudentCategoriesResponseDto> getStudentCategoryById(@PathVariable("id") String id) {
		StudentCategoriesResponseDto response = studentCategoriesService.getStudentCategoryById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<StudentCategoriesResponseDto>(), response,
				Translator.toLocale("student.categories.fetch.success", null),
				Translator.toLocale("student.categories.fetch.failed", null));
	}

	@GetMapping("/all")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<StudentCategoriesResponseDto>> getAllStudentCategories() {
		List<StudentCategoriesResponseDto> response = studentCategoriesService.getAllStudentCategories();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<StudentCategoriesResponseDto>>(), response,
				Translator.toLocale("student.categories.fetch.all.success", null),
				Translator.toLocale("student.categories.fetch.all.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> deleteStudentCategoryById(@PathVariable("id") String id) {
		Boolean response = studentCategoriesService.deleteStudentCategoryById(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("student.categories.deleted.success", null),
				Translator.toLocale("student.categories.deleted.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = studentCategoriesService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/{id}/mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<StudentCategoryMappingResponse> getAllMappingsForStudentCategory
			(@PathVariable("id") String id) {
		StudentCategoryMappingResponse response = studentCategoriesService
				.getAllMappingsForStudentCategory(id);
		return ResponseHelper.createResponse(new LMSResponse<NameCommonResponseDto>(), response,
				Translator.toLocale("student.category.mappings.fetch.success", null),
				Translator.toLocale("student.category.mappings.fetch.failed", null));

	}
}
