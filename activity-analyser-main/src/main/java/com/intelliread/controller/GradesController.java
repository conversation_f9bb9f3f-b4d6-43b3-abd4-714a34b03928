package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.GradeSectionResponseDto;
import com.intelliread.request.dto.GradesRequestDto;
import com.intelliread.response.dto.ConfirmationApiResponseDto;
import com.intelliread.response.dto.GradeSectionSubjectResponseDto;
import com.intelliread.response.dto.GradesResponseDto;
import com.intelliread.response.dto.GradesSectionFeignResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.PaginatedResponse;
import com.intelliread.response.teachercontent.GradeContentResponseDto;
import com.intelliread.services.GradesService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

@RestController
@RequestMapping(value = "/v1/api/master/grades")
public class GradesController {

	@Autowired
	private GradesService gradesService;

	@PostMapping()
	@SuppressWarnings("unchecked")
	public LMSResponse<GradesResponseDto> createGrades(@Valid @RequestBody GradesRequestDto request) {
		GradesResponseDto response = gradesService.createGrades(request);
		return ResponseHelper.createResponse(new LMSResponse<GradesResponseDto>(), response,
				Translator.toLocale("grades.created.success", null), Translator.toLocale("grades.create.failed", null));
	}

	@PutMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<GradesResponseDto> updateGrades(@PathVariable("id") String id,
			@Valid @RequestBody GradesRequestDto request) {
		GradesResponseDto response = gradesService.updateGrades(id, request);
		return ResponseHelper.createResponse(new LMSResponse<GradesResponseDto>(), response,
				Translator.toLocale("grades.update.success", null), Translator.toLocale("grades.update.failed", null));
	}

	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<GradesResponseDto> getGradesById(@PathVariable("id") String id) {
		GradesResponseDto response = gradesService.getGradesById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<GradesResponseDto>(), response,
				Translator.toLocale("grades.fetch.success", null), Translator.toLocale("grades.fetch.failed", null));
	}

	@GetMapping("/all")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<GradesResponseDto>> getAllGrades() {
		List<GradesResponseDto> response = gradesService.getAllGrades();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradesResponseDto>>(), response,
				Translator.toLocale("grades.fetch.all.success", null),
				Translator.toLocale("grades.fetch.all.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> deleteGradesById(@PathVariable("id") String id) {
		Boolean response = gradesService.deleteGradesById(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("grades.deleted.success", null),
				Translator.toLocale("grades.deleted.failed", null));
	}

	@GetMapping("/all-by-ids")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<GradesResponseDto>> getAllGradesByIds(@RequestParam("ids") List<String> ids) {
		List<GradesResponseDto> response = gradesService.getAllGradesByIds(ids);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradesResponseDto>>(), response,
				Translator.toLocale("grades.fetch.all.success", null),
				Translator.toLocale("grades.fetch.all.failed", null));
	}

	@GetMapping("/is-exist")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> existGradesByIds(@RequestParam("ids") List<String> ids) {
		Boolean response = gradesService.existGradesByIds(ids);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("grades.exist.succes", null), Translator.toLocale("grades.exist.failed", null));
	}

	@GetMapping("/toggle-active")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> toggleActiveStatus(@RequestParam("id") String id,
			@RequestParam("active") boolean active) {
		Boolean response = gradesService.toggleActiveStatus(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("active.status.toggle.success", null),
				Translator.toLocale("active.status.toggle.failed", null));
	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	public LMSResponse<ConfirmationApiResponseDto> confirmationApiForGrade(@RequestParam("id") String id,
			@RequestParam(value = "operationType") String operationType) {
		ConfirmationApiResponseDto response = gradesService.getMappedData(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("grades.mapped.data.success", null),
				Translator.toLocale("grades.mapped.data.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = gradesService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/by-name/{name}")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getGradeIdByName(@PathVariable("name") String name) {
		String response = gradesService.getGradeIdByName(name);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("grades.fetch.success", null), Translator.toLocale("grades.fetch.failed", null));
	}

	@Lazy
	@GetMapping("/page")
	@SuppressWarnings("unchecked")
	public LMSResponse<PaginatedResponse<GradesResponseDto>> getAllGradesByPagination(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<GradesResponseDto> response = gradesService.getAllGradesByPagination(pageNumber, pageSize,
				sortBy, sortOrder, search, active);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<PaginatedResponse<GradesResponseDto>>(), response,
				Translator.toLocale("grades.fetch.all.success", null),
				Translator.toLocale("grades.fetch.all.failed", null));
	}

	@Lazy
	@GetMapping("/for-grade-section-mapping")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<GradesResponseDto>> getAllGradesForSectionMapping(
			@RequestParam(value = "planId", required = true) String planId,
			@RequestParam(value = "boardId", required = true) String boardId) {
		List<GradesResponseDto> response = gradesService.getAllGradesForSectionMapping(planId, boardId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradesResponseDto>>(), response,
				Translator.toLocale("grades.plan.fetch.all.success", null),
				Translator.toLocale("grades.plan.fetch.all.failed", null));
	}

	@Lazy
	@PutMapping("/assigned-grades-sections")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<GradesSectionFeignResponseDto>> assignedGradesAndSectionForAcademicStaffs(
			@RequestBody List<GradeSectionResponseDto> request) {
		List<GradesSectionFeignResponseDto> response = gradesService
				.findTheAssignedGradesAndSectionForAcademicStaffs(request);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradesSectionFeignResponseDto>>(), response,
				Translator.toLocale("grades.fetch.all.success", null),
				Translator.toLocale("grades.fetch.all.failed", null));
	}
	
	
	
	@Lazy
	@GetMapping("/grade-section-subject-subtopic-academicyear")
	@SuppressWarnings("unchecked")
	public LMSResponse<GradeSectionSubjectResponseDto> getGradeSectionSubjectSubTopicAndAcademicYear(
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId) {
		GradeSectionSubjectResponseDto response = gradesService.getGradeSectionSubjectSubTopicAndAcademicYear(gradeId,
				sectionId, subjectId, subTopicId, academicYearId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<GradeSectionSubjectResponseDto>(), response,
				Translator.toLocale("grade.section.subject.subtopic.academicyear.success", null),
				Translator.toLocale("grade.section.subject.subtopic.academicyear.failed", null));
	}
	
	@Lazy
	@GetMapping("/from-subject-mapping")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<GradesResponseDto>> findAllGradesWithBoardId(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "search", required = false) String search) {
		List<GradesResponseDto> response = gradesService.findAllGradesWithBoardId(boardId, search);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<GradesResponseDto>(), response,
				Translator.toLocale("grades.board.success", null), Translator.toLocale("grades.board.failed", null));
	}
	
	
	@GetMapping("section/subjects/sub-topics/chapter/feigncall")
	@SuppressWarnings( "unchecked")
	public LMSResponse<List<GradeContentResponseDto>> getAllGradeSubjectsChapterDetailsList() {
		List<GradeContentResponseDto> response = gradesService.getAllGradeSubjectsChapterDetailsList();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradeContentResponseDto>>(), response,
				Translator.toLocale("subjects.fetch.all.success", null),
				Translator.toLocale("subjects.fetch.all.failed", null));
	}

}
