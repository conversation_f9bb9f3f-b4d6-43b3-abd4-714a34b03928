package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.config.RabbitMQConfig;
import com.intelliread.constants.ActivityConstants;
import com.intelliread.model.Activity;
import com.intelliread.activity.types.ActivityType;
import com.intelliread.entity.master.UserActivity;
import com.intelliread.response.ApiResponse;
import com.intelliread.response.Pagination;
import com.intelliread.response.activityanalyser.dto.APIResponse;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.impl.ActivityConsumerServiceImpl;
import com.intelliread.utilities.NumberUtils;
import com.intelliread.utilities.ResponseHelper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;

@RestController
@RequestMapping("/v1/api/activities")
public class ActivityController {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ActivityConsumerServiceImpl activityConsumerServiceImpl;

    @PostMapping
    public ResponseEntity<?> handleActivity(@Valid @RequestBody Activity activity) {
        try {
            // Validate activity
            if (!ActivityType.isValidType(activity.getType())) {
                return ResponseEntity.badRequest().body(new ApiResponse<>("error", "Invalid activity type."));
            }

            // Send message to RabbitMQ
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.EXCHANGE_NAME,
                    RabbitMQConfig.ROUTING_KEY,
                    activity
            );

            return ResponseEntity.ok().body(new ApiResponse<>("Activity submitted successfully."));
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return ResponseEntity.status(500).body(new ApiResponse<>("error", "Failed to process activity."));
        }
    }

    @GetMapping("/{userId}")
    public ResponseEntity<?> getActivitiesByUserId(@PathVariable String userId, HttpServletRequest request) {
        String activityType = request.getParameter("activity_type");
        int page = NumberUtils.parseIntWithDefault(request.getParameter("page"), 0);
        int size = NumberUtils.parseIntWithDefault(request.getParameter("limit"), 5);

        if (page > 0) {
            page--;
        }

        Page<UserActivity> activities;

        if (activityType != null && activityType.length() > 1 && ActivityConstants.isValidType(activityType)) {
            activities = activityConsumerServiceImpl.getPaginatedUserActivitiesByActivityType(userId, activityType, page, size);
        } else {
            activities = activityConsumerServiceImpl.getPaginatedUserActivities(userId, page, size);
        }

        Pagination pagination = new Pagination(
                activities.getNumber() + 1,  // current page (1-based)
                activities.getTotalPages(),
                activities.getTotalElements(),
                activities.getSize()
        );

        return ResponseEntity.ok().body(new ApiResponse<>(activities.getContent(), pagination));
    }

    @GetMapping("/{userId}/course/{courseId}")
    @SuppressWarnings("all")
    public LMSResponse<APIResponse> getActivitiesByCourseAndUserId(@PathVariable String userId, @PathVariable String courseId, HttpServletRequest request) {
        String sectionId = request.getParameter("section_id");
        String chapterId = request.getParameter("chapter_id");
        int page = NumberUtils.parseIntWithDefault(request.getParameter("page"), 0);
        int size = NumberUtils.parseIntWithDefault(request.getParameter("limit"), 5);

        if (page > 0) {
            page--;
        }

        APIResponse response = new APIResponse();
        Page<UserActivity> activityList = null;

        if ((sectionId != null && !sectionId.isBlank()) && (chapterId !=null && !chapterId.isBlank())) {
            activityList = activityConsumerServiceImpl.getActivitiesByCourseAndUserId(userId, courseId, sectionId, chapterId, page, size);
        } else if (sectionId != null && !sectionId.isBlank()) {
            activityList = activityConsumerServiceImpl.getActivitiesByCourseAndUserId(userId, courseId, sectionId, page, size);
        } else {
            activityList = activityConsumerServiceImpl.getActivitiesByCourseAndUserId(userId, courseId, page, size);
        }

        response.setData(activityList.getContent());
        response.setPageNo(activityList.getNumber() + 1);
        response.setPageSize(activityList.getSize());
        response.setTotalElements(activityList.getTotalElements());
        response.setTotalPages(activityList.getTotalPages());
        response.setLast(activityList.isLast());

        return ResponseHelper.createResponse(new LMSResponse<APIResponse>(), response,
                Translator.toLocale("activity.analyser.response.success", null),
                Translator.toLocale("activity.analyser.response.failed", null));
    }
}