package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.enums.CourseMode;
import com.intelliread.enums.CourseStatus;
import com.intelliread.feign.users.FileCategories;
import com.intelliread.feign.users.SubFolders;
import com.intelliread.request.dto.ApprovalResponse;
import com.intelliread.request.dto.OnboardStudentRequestDTO;
import com.intelliread.response.courses.*;
import com.intelliread.response.dto.*;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.PaginatedResponse;
import com.intelliread.services.CoursesService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

@RestController
@RequestMapping(value = "/v1/api/master/courses")
public class CoursesController {
	
	@Autowired
	private CoursesService coursesService;

	@PostMapping()
	@SuppressWarnings("all")
	public LMSResponse<APIResponseDto> createCourses(@Valid @RequestBody CoursesRequestDto request) {
		APIResponseDto response = coursesService.createCourses(request);
		return ResponseHelper.createResponse(new LMSResponse<APIResponseDto>(), response,
				Translator.toLocale("courses.created.success", null),
				Translator.toLocale("courses.created.failed", null));
	}

	@PutMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<APIResponseDto> updateCourses(@PathVariable("id") String id,
			@Valid @RequestBody CoursesRequestDto request) {
		APIResponseDto response = coursesService.updateCourses(id, request);
		return ResponseHelper.createResponse(new LMSResponse<APIResponseDto>(), response,
				Translator.toLocale("courses.update.success", null),
				Translator.toLocale("courses.update.failed", null));
	}


	@PutMapping("/status/{id}/{transitionStatus}")
	@SuppressWarnings("all")
	public LMSResponse<APIResponseDto> updateCoursesStatus(@PathVariable("id") String id,
			@PathVariable("transitionStatus") String transitionStatus) {
		APIResponseDto response = coursesService.updateCoursesStatus(id, transitionStatus);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<APIResponseDto>(), response,
				Translator.toLocale("courses.status.update.success", null),
				Translator.toLocale("courses.status.update.failed", null));
	}



	@PutMapping("/tags/{id}")
	@SuppressWarnings("all")
	public LMSResponse<TagsResponse> tags(@PathVariable("id") String id,
			@Valid @RequestBody TagsRequestDto tags) {
		TagsResponse response = coursesService.tags(id, tags);
		return ResponseHelper.createResponse(new LMSResponse<TagsResponse>(), response,
				Translator.toLocale("courses.tag.added.success", null),
				Translator.toLocale("courses.tag.added.failed", null));
	}

//	@PutMapping("/remove_tags/{id}")
//	@SuppressWarnings("all")
//	public LMSResponse<TagsResponse> removeTagsCourses(@PathVariable("id") String id,
//			@Valid @RequestParam("removeTag") String removeTag) {
//		TagsResponse response = coursesService.removeTagsCourses(id, removeTag);
//		return ResponseHelper.createResponse(new LMSResponse<TagsResponse>(), response,
//				Translator.toLocale("courses.tag.removed.success", null),
//				Translator.toLocale("courses.tag.removed.failed", null));
//	}

	@PutMapping("/prerequisites/{id}")
	@SuppressWarnings("all")
	public LMSResponse<PrerequisteResponse> prequisties(@PathVariable("id") String id,
			@Valid @RequestBody Prerequisite addPrequisties) {
		PrerequisteResponse response = coursesService.prequisties(id, addPrequisties);
		return ResponseHelper.createResponse(new LMSResponse<PrerequisteResponse>(), response,
				Translator.toLocale("courses.prequisties.added.success", null),
				Translator.toLocale("courses.prequisties.added.failed", null));
	}

//	@PutMapping("/remove_prequisties/{id}")
//	@SuppressWarnings("all")
//	public LMSResponse<PrerequisteResponse> removePrequistiesCourses(@PathVariable("id") String id,
//			@Valid @RequestBody Prerequisite removePrequisties) {
//		PrerequisteResponse response = coursesService.removePrequistiesCourses(id, removePrequisties);
//		return ResponseHelper.createResponse(new LMSResponse<PrerequisteResponse>(), response,
//				Translator.toLocale("courses.prequisties.removed.success", null),
//				Translator.toLocale("courses.prequisties.removed.failed", null));
//	}

	@PostMapping("/versionHistory/{id}")
	@SuppressWarnings("all")
	public LMSResponse<VersionHistoryResponseDto> createNewVersion(@PathVariable("id") String id) {
		VersionHistoryResponseDto response = coursesService.createNewVersion(id);
		return ResponseHelper.createResponse(new LMSResponse<VersionHistoryResponseDto>(), response,
				Translator.toLocale("courses.new.version.added.success", null),
				Translator.toLocale("courses.new.version.added.failed", null));
	}


	@PutMapping("/approval/{id}/{approvalStatus}")
	@SuppressWarnings("all")
	public LMSResponse<ApprovalHistoryResponse> addapprovalHistory(@PathVariable("id") String id,
			@PathVariable("approvalStatus") String approvalStatus) {
		ApprovalHistoryResponse response = coursesService.addapprovalHistory(id, approvalStatus);
		return ResponseHelper.createResponse(new LMSResponse<ApprovalHistoryResponse>(), response,
				Translator.toLocale("courses.approval.response.added.success", null),
				Translator.toLocale("courses.approval.response.added.failed", null));
	}
	
//	@PutMapping("/remove_approval_response/{id}")
//	@SuppressWarnings("all")
//	public LMSResponse<ApprovalHistoryResponse> removeApprovalResponseCourses(@PathVariable("id") String id,
//			@Valid @RequestBody ApprovalHistory addApprovalResponse) {
//		ApprovalHistoryResponse response = coursesService.removeApprovalResponseCourses(id, addApprovalResponse);
//		return ResponseHelper.createResponse(new LMSResponse<ApprovalHistoryResponse>(), response,
//				Translator.toLocale("courses.approval.response.removed.success", null),
//				Translator.toLocale("courses.approval.response.removed.failed", null));
//	}

	@SuppressWarnings("all")
	@GetMapping("/{id}")
	public LMSResponse<CoursesGetApiDto> course(@PathVariable(value = "id", required = true) String id) {
		CoursesGetApiDto response = coursesService.course(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<CoursesGetApiDto>(), response,
				Translator.toLocale("courses.get.by.id.success", null),
				Translator.toLocale("courses.get.by.id.failed", null));
	}

	@SuppressWarnings("all")
	@GetMapping("/approvalHistory/{id}")
	public LMSResponse<ApprovalResponse> approvalHistory(@PathVariable(value = "id", required = true) String id) {
		ApprovalResponse response = coursesService.approvalHistory(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ApprovalResponse>(), response,
				Translator.toLocale("courses.approval.history.get.by.id.success", null),
				Translator.toLocale("courses.approval.history.get.by.id.failed", null));
	}

	@SuppressWarnings("all")
	@GetMapping("/versionHistory/{id}")
	public LMSResponse<GetVersionResponseDto> versionHistory(@PathVariable(value = "id", required = true) String id) {
		GetVersionResponseDto response = coursesService.versionHistory(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<GetVersionResponseDto>(), response,
				Translator.toLocale("courses.version.history.get.success", null),
				Translator.toLocale("courses.version.history.get.failed", null));
	}
	
	@SuppressWarnings("all")
	@GetMapping("/updateHistory/{id}")
	public LMSResponse<UpdateHistoryResponse> updateHistory(@PathVariable(value = "id", required = true) String id) {
		UpdateHistoryResponse response = coursesService.updateHistory(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<UpdateHistoryResponse>(), response,
				Translator.toLocale("courses.update.history.get.success", null),
				Translator.toLocale("courses.update.history.get.failed", null));
	}
	
	@SuppressWarnings("all")
	@GetMapping("/pendingApproval/{application}/{courseStatus}")
	public LMSResponse<ListCourseDto> getPendingCourses(@PathVariable (value = "application", required = true) String application,@PathVariable (value = "courseStatus", required = true) String courseStatus) {
		ListCourseDto response = coursesService.getPendingCourses(application,courseStatus);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ListCourseDto>(), response,
				Translator.toLocale("courses.retrieved.success", null),
				Translator.toLocale("courses.retrieve.failed", null));
	}

	@SuppressWarnings("all")
	@GetMapping()
	public LMSResponse<APIResponse> getCourses(
			  @RequestParam(value = "pageNo", defaultValue = "0", required = true) @Min(0) int pageNo,
	            @RequestParam(value = "pageSize", defaultValue = "10", required = true) @Min(1) @Max(50) int pageSize,
				@RequestParam(value = "application", required = false) String application,
				@RequestParam(value = "courseStatus", required = false) CourseStatus courseStatus,
				@RequestParam(value = "id", required = false) String id,
				@RequestParam(value = "courseName", required = false) String courseName,
				@RequestParam(value = "code", required = false) String code,
				@RequestParam(value = "type", required = false) String type,
				@RequestParam(value = "courseMode", required = false) CourseMode courseMode,
				@RequestParam(value = "createdUserName", required = false) String createdUserName,
				@RequestParam(value = "search", required = false) String search,
			  	@RequestParam(value = "category", required = false) String category,
				@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
				@RequestParam(value = "sortBy", required = false, defaultValue = "course_status") String sortBy
	) {
    	APIResponse response=coursesService.getCourses(pageNo, pageSize,application,courseStatus,id,courseName,code,type,courseMode,createdUserName,search,sortOrder,sortBy,category);
    	return ResponseHelper.responseForGetOrFeign(new LMSResponse<APIResponse>(), response,
				Translator.toLocale("courses.retrieved.success", null),
				Translator.toLocale("courses.retrieve.failed", null));
	}

	@SuppressWarnings("all")
	@GetMapping("/list")
	public LMSResponse<PaginatedResponse<CoursesListApiDto>> getCoursesList(
			@RequestParam(value = "pageNo", defaultValue = "0", required = true) @Min(0) int pageNo,
			@RequestParam(value = "pageSize", defaultValue = "10", required = true) @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "application", required = false) String application,
			@RequestParam(value = "courseStatus", required = false) CourseStatus courseStatus,
			@RequestParam(value = "id", required = false) String id,
			@RequestParam(value = "courseName", required = false) String courseName,
			@RequestParam(value = "code", required = false) String code,
			@RequestParam(value = "type", required = false) String type,
			@RequestParam(value = "courseMode", required = false) CourseMode courseMode,
			@RequestParam(value = "createdUserName", required = false) String createdUserName,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "category", required = false) String category,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "sortBy", required = false, defaultValue = "course_status") String sortBy
	) {
		PaginatedResponse<CoursesListApiDto> response=coursesService.getCoursesList(pageNo, pageSize,application,courseStatus,id,courseName,code,type,courseMode,createdUserName,search,sortOrder,sortBy,category);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<PaginatedResponse<CoursesListApiDto>>(), response,
				Translator.toLocale("courses.retrieved.success", null),
				Translator.toLocale("courses.retrieve.failed", null));
	}
	
	@Lazy
	@SuppressWarnings("unchecked")
	@PutMapping("/upload/{id}")
	public LMSResponse<String> fileUpload(@PathVariable("id") String id,@RequestParam(name = "fileCategory") FileCategories category,@RequestParam(name = "subFolders") SubFolders subFolders,@RequestParam(name = "fileType") String fileType, @RequestParam(name = "file") MultipartFile file) {
		LMSResponse<String> response = coursesService.fileUpload(id,category,subFolders,fileType, file);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("courses.fileupload.success", null), Translator.toLocale("courses.fileupload.failed", null));
	}
	
	
	@DeleteMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<DeleteCourse> deleteCourse(@PathVariable("id") String id) {
		DeleteCourse response = coursesService.deleteCourse(id);
		return ResponseHelper.createResponse(new LMSResponse<DeleteCourse>(), response,
				Translator.toLocale("courses.deleted.success", null),
				Translator.toLocale("courses.delete.failed", null));
	}
	
	
	@PostMapping("/clone/{id}")
	@SuppressWarnings("all")
	public LMSResponse<CloneCourseResponse> cloneCourses(@PathVariable("id") String id) {
		CloneCourseResponse response = coursesService.cloneCourses(id);
		return ResponseHelper.createResponse(new LMSResponse<CloneCourseResponse>(), response,
				Translator.toLocale("courses.cloned.success", null),
				Translator.toLocale("courses.clone.failed", null));
	}
	
	@PostMapping("/onboard")
	@SuppressWarnings("all")
	public LMSResponse<String> onboardStudent(@Valid @RequestBody OnboardStudentRequestDTO onboardStudentRequestDTO) {
		String response = coursesService.onboardStudent(onboardStudentRequestDTO);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("onboard.student.success", null),
				Translator.toLocale("onboard.student.failed", null));
	}
	
	@Lazy
	@SuppressWarnings("unchecked")	
	@PostMapping("/users/approve")
	public LMSResponse<String> coursesApprove(@Valid @RequestBody OnboardStudentRequestDTO onboardStudentRequestDTO) {
		String response = coursesService.coursesApprove(onboardStudentRequestDTO);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("courses.approve.success", null),
				Translator.toLocale("courses.approve.failed", null));
	}
	
	@Lazy
	@SuppressWarnings("unchecked")	
	@PostMapping("/users/promote")
	public LMSResponse<String> userPromote(@Valid @RequestBody OnboardStudentRequestDTO onboardStudentRequestDTO) {
		String response = coursesService.userPromote(onboardStudentRequestDTO);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("user.promote.success", null),
				Translator.toLocale("user.promote.failed", null));
	}	
}
