package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.entity.master.PaymentTransaction;
import com.intelliread.enums.ErrorCodes;
import com.intelliread.exception.MSException;
import com.intelliread.request.dto.PaymentTransactionRequestDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.PaymentUserService;
import com.intelliread.utilities.ResponseHelper;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for handling PaymentTransaction-related APIs.
 * PaymentTransactions are the payments required for the students (in B2C model) to make in order to take the courses.
 * Includes a POST endpoint to create a new payment transaction.
 *
 * <AUTHOR>
 * @since 1.0.1
 */
@RestController
@RequestMapping("/v1/api/payment-transactions")
public class PaymentTransactionController {

    private final PaymentUserService paymentUserService;

    @Autowired
    public PaymentTransactionController(PaymentUserService paymentUserService) {
        this.paymentUserService = paymentUserService;
    }

    /**
     * Creates a new payment transaction.
     *
     * @param paymentTransaction the payment transaction to be created
     * @return LMSResponse<PaymentTransactionResponseDto> with the created transaction or error message
     */
    @PostMapping
    @SuppressWarnings("all")
    public LMSResponse<PaymentTransaction> createTransaction(@Valid @RequestBody PaymentTransactionRequestDto paymentTransaction) {
        try {
            PaymentTransaction transaction = paymentUserService.createTransaction(paymentTransaction);
            return ResponseHelper.createResponse(new LMSResponse<PaymentTransaction>(), transaction,
                    Translator.toLocale("paymenttransaction.create.success", null),
                    Translator.toLocale("paymenttransaction.create.failed", null));
        } catch (Exception e) {
            throw new MSException(ErrorCodes.INTERNAL_SERVER_ERROR, "Error creating transaction: " + e.getMessage());
        }
    }
}
