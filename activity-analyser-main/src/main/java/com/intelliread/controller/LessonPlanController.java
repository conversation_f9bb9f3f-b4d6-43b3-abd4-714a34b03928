package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.GetLessonPlanResponseDto;
import com.intelliread.request.dto.LessonPlanRequestDto;
import com.intelliread.response.dto.DeleteLessonPlanDto;
import com.intelliread.response.dto.LessonPlanResponseDto;
import com.intelliread.response.dto.UpdateHistoryResponse;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.LessonPlanService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/v1/api/master/lessonPlan")
public class LessonPlanController {
	
	@Autowired
	LessonPlanService lessonPlanService;
	
	@PostMapping("/{courseId}")
	@SuppressWarnings("all")
	public LMSResponse<LessonPlanResponseDto> createLessonPlan(@PathVariable("courseId") String courseId,
			@Valid @RequestBody LessonPlanRequestDto request) {
		LessonPlanResponseDto response = lessonPlanService.createLessonPlan(courseId, request);
		return ResponseHelper.createResponse(new LMSResponse<LessonPlanResponseDto>(), response,
				Translator.toLocale("lessonplan.created.success", null),
				Translator.toLocale("lessonplan.create.failed", null));
	}
	
	@PutMapping("/{lessonPlanId}")
	@SuppressWarnings("all")
	public LMSResponse<LessonPlanResponseDto> updateLessonPlan(@PathVariable("lessonPlanId") String lessonPlanId,
			@Valid @RequestBody LessonPlanRequestDto request) {
		LessonPlanResponseDto response = lessonPlanService.updateLessonPlan(lessonPlanId, request);
		return ResponseHelper.createResponse(new LMSResponse<LessonPlanResponseDto>(), response,
				Translator.toLocale("lessonplan.updated.success", null),
				Translator.toLocale("lessonplan.update.failed", null));
	}

	@DeleteMapping("/{id}/{courseId}")
	@SuppressWarnings("all")
	public LMSResponse<DeleteLessonPlanDto> deleteLessonPlan(@PathVariable("id") String id,@PathVariable("courseId") String courseId) {
		DeleteLessonPlanDto response = lessonPlanService.deleteLessonPlan(id,courseId);
		return ResponseHelper.createResponse(new LMSResponse<DeleteLessonPlanDto>(), response,
				Translator.toLocale("lessonplan.delete.success", null),
				Translator.toLocale("lessonplan.delete.failed", null));
	}
	
	@GetMapping("/{courseId}")
	@SuppressWarnings("all")
	public LMSResponse<GetLessonPlanResponseDto> getLessonPlan(@PathVariable("courseId") String courseId) {
		GetLessonPlanResponseDto response = lessonPlanService.getLessonPlan(courseId);
		return ResponseHelper.createResponse(new LMSResponse<GetLessonPlanResponseDto>(), response,
				Translator.toLocale("lessonplan.retrieved.success", null),
				Translator.toLocale("lessonplan.retrieved.failed", null));
	}
	
	
	@GetMapping("/updateHistory/{id}")
	@SuppressWarnings("all")
	public LMSResponse<UpdateHistoryResponse> getUpdateHistory(@PathVariable("id") String id) {
		UpdateHistoryResponse response = lessonPlanService.getUpdateHistory(id);
		return ResponseHelper.createResponse(new LMSResponse<UpdateHistoryResponse>(), response,
				Translator.toLocale("lessonplan.retrieved.success", null),
				Translator.toLocale("lessonplan.retrieved.failed", null));
	}
	
}
