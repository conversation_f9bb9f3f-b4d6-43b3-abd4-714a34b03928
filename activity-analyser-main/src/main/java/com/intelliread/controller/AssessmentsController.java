package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.entity.master.AssessmentAnswersActivity;
import com.intelliread.request.dto.*;
import com.intelliread.response.ApiResponse;
import com.intelliread.response.dto.*;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.AssessmentsService;
import com.intelliread.services.AssignmentProgressService;
import com.intelliread.services.impl.ActivityConsumerServiceImpl;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(value = "/v1/api/master/assessments")
public class AssessmentsController {
	
	@Autowired
	AssessmentsService assessmentsService;

	@Autowired
	private ActivityConsumerServiceImpl activityConsumerServiceImpl;

	@Autowired
	private AssignmentProgressService assignmentProgressService;
	
	@PostMapping()
	@SuppressWarnings("all")
	public LMSResponse<AssessmentsResponse> createAssessments(@Valid @RequestBody AssessmentsRequest request) {
		AssessmentsResponse response = assessmentsService.createAssessments(request);
		return ResponseHelper.createResponse(new LMSResponse<AssessmentsResponse>(), response,
				Translator.toLocale("assessments.created.success", null),
				Translator.toLocale("assessments.create.failed", null));
	}
	
	@PutMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<AssessmentsResponse> updateAssessments(@PathVariable("id") String id,@Valid @RequestBody AssessmentsRequest request) {
		AssessmentsResponse response = assessmentsService.updateAssessments(id,request);
		return ResponseHelper.createResponse(new LMSResponse<AssessmentsResponse>(), response,
				Translator.toLocale("assessments.updated.success", null),
				Translator.toLocale("assessments.update.failed", null));
	}
	
	@PutMapping("/{id}/{status}")
	@SuppressWarnings("all")
	public LMSResponse<UpdateStatusAssessmentsResponse> updateAssessmentsStatus(@PathVariable("id") String id,@PathVariable("status") String status) {
		UpdateStatusAssessmentsResponse response = assessmentsService.updateAssessmentsStatus(id,status);
		return ResponseHelper.createResponse(new LMSResponse<UpdateStatusAssessmentsResponse>(), response,
				Translator.toLocale("assessments.updated.success", null),
				Translator.toLocale("assessments.update.failed", null));
	}
	
	@DeleteMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<DeleteAssessmentResponsee> deleteAssessment(@PathVariable("id") String id) {
		DeleteAssessmentResponsee response = assessmentsService.deleteAssessment(id);
		return ResponseHelper.createResponse(new LMSResponse<DeleteAssessmentResponsee>(), response,
				Translator.toLocale("assessments.deleted.success", null),
				Translator.toLocale("assessments.delete.failed", null));
	}
	
	@PutMapping("/addQuestions/{id}")
	@SuppressWarnings("all")
	public LMSResponse<ListAssessmentsResponse> addQuestionsAssessments(@PathVariable("id") String id,@Valid @RequestBody ListAssessmentsQuestions request) {
		ListAssessmentsResponse response = assessmentsService.addQuestionsAssessments(id,request);
		return ResponseHelper.createResponse(new LMSResponse<ListAssessmentsResponse>(), response,
				Translator.toLocale("assessments.questions.added.success", null),
				Translator.toLocale("assessments.questions.added.failed", null));
	}
	
	@PutMapping("/removeQuestions/{id}")
	@SuppressWarnings("all")
	public LMSResponse<ListAssessmentsResponse> removeQuestions(@PathVariable("id") String id,@Valid @RequestBody ListRemoveAssessmentsQuestions request) {
		ListAssessmentsResponse response = assessmentsService.removeQuestions(id,request);
		return ResponseHelper.createResponse(new LMSResponse<ListAssessmentsResponse>(), response,
				Translator.toLocale("assessments.questions.removed.success", null),
				Translator.toLocale("assessments.questions.remove.failed", null));
	}
	
	@PostMapping("/publish/{id}")
	@SuppressWarnings("all")
	public ResponseEntity<?> createAssessmentsQuestions(@Valid @PathVariable("id") String id) {
		AssessmentsQuestionsResponse response = assessmentsService.createAssessmentsQuestions(id);
		return new ResponseEntity<AssessmentsQuestionsResponse>(response, HttpStatus.OK);
	}
	
	
	@PutMapping("/lesson-plan/chapterId/{id}")
	@SuppressWarnings("all")
	public ResponseEntity<?> publishLessonPlan(@PathVariable("id") String id,@RequestBody PublishAssessmentstoLessonPlan dto) {
		GetLessonPlanResponseDto response = assessmentsService.publishLessonPlan(id,dto);
		return new ResponseEntity<GetLessonPlanResponseDto>(response, HttpStatus.OK);

	}
	
	@PutMapping("/lesson-plan/sectionId/{id}")
	@SuppressWarnings("all")
	public ResponseEntity<?> publishSectionLessonPlan(@PathVariable("id") String id,@RequestBody PublishAssessmentstoLessonPlan dto) {
		GetLessonPlanResponseDto response = assessmentsService.publishSectionLessonPlan(id,dto);
		return new ResponseEntity<GetLessonPlanResponseDto>(response, HttpStatus.OK);

	}
	
	@PutMapping("/lesson-plan/courseId/{id}")
	@SuppressWarnings("all")
	public ResponseEntity<?> publishCourseLessonPlan(@PathVariable("id") String id,@RequestBody PublishAssessmentstoLessonPlan dto) {
		GetLessonPlanResponseDto response = assessmentsService.publishCourseLessonPlan(id,dto);
		return new ResponseEntity<GetLessonPlanResponseDto>(response, HttpStatus.OK);

	}
	
	
	@GetMapping("/{id}")
	@SuppressWarnings("all")
	public LMSResponse<AssessmentsResponseDto> getAssessments(@PathVariable("id") String id) {
		AssessmentsResponseDto response = assessmentsService.getAssessments(id);
		return ResponseHelper.createResponse(new LMSResponse<AssessmentsResponseDto>(), response,
				Translator.toLocale("assessments.retrieved.success", null),
				Translator.toLocale("assessments.retrieve.failed", null));
	}
	
	
	@GetMapping("")
	@SuppressWarnings("all")
	public LMSResponse<GetAllAssessmentsList> getAllAssessments() {
		GetAllAssessmentsList response = assessmentsService.getAllAssessments();
		return ResponseHelper.createResponse(new LMSResponse<GetAllAssessmentsList>(), response,
				Translator.toLocale("assessments.retrieved.success", null),
				Translator.toLocale("assessments.retrieve.failed", null));
	}

	@GetMapping("/")
	@SuppressWarnings("all")
	public LMSResponse<GetAllAssessmentsList> getAllAssessmentsList() {
		GetAllAssessmentsList response = assessmentsService.getAllAssessments();
		return ResponseHelper.createResponse(new LMSResponse<GetAllAssessmentsList>(), response,
				Translator.toLocale("assessments.retrieved.success", null),
				Translator.toLocale("assessments.retrieve.failed", null));
	}

	@PostMapping("/studentAssessmentResult/{assessmentId}/{assigneeId}")
	@SuppressWarnings("all")
	public LMSResponse<StudentAssessmentsResultResponse> addStudentAssessmentsResult(@Valid @PathVariable ("assessmentId") String assessmentId,@Valid @PathVariable ("assigneeId") String assigneeId,@Valid @RequestBody List<StudentAssessmentResultRequest> request) {
		StudentAssessmentsResultResponse response = assessmentsService.addStudentAssessmentsResult(assessmentId,assigneeId,request);
		return ResponseHelper.createResponse(new LMSResponse<ActivitiesResponse>(), response,
				Translator.toLocale("studentassessmentsresult.added.success", null),
				Translator.toLocale("studentassessmentsresult.added.failed", null));
	}
	
	@GetMapping("/studentAssessmentResult/{assessmentId}/{assigneeId}")
	@SuppressWarnings("all")
	public LMSResponse<StudentAssessmentsResultResponse> getStudentAssessmentsResult(@Valid @PathVariable ("assessmentId") String assessmentId,@Valid @PathVariable ("assigneeId") String assigneeId) {
		StudentAssessmentsResultResponse response = assessmentsService.getStudentAssessmentsResult(assessmentId,assigneeId);
		return ResponseHelper.createResponse(new LMSResponse<ActivitiesResponse>(), response,
				Translator.toLocale("studentassessmentsresult.retrieved.success", null),
				Translator.toLocale("studentassessmentsresult.retrieved.failed", null));
	}

	@PostMapping("/answers")
	@SuppressWarnings("all")
	public LMSResponse<AssessmentAnswersActivity> handleAssessmentAnswers(@Valid @RequestBody AssessmentAnswerActivityRequestDto activity) {
		AssessmentAnswersActivity activity1 = assignmentProgressService.createOrUpdateAssessmentAnswerActivity(activity);
		return ResponseHelper.createResponse(new LMSResponse<AssessmentAnswersActivity>(), activity,
				Translator.toLocale("assessment.answers.activity.save.success", null),
				Translator.toLocale("assessment.answers.activity.save.failed", null));
	}

	@GetMapping("/{assessmentId}/answers")
	@SuppressWarnings("all")
	public LMSResponse<AssessmentAnswerActivityResponseDto> getAssessmentAnswers(@PathVariable String assessmentId) {
		AssessmentAnswerActivityResponseDto  responseDto = assignmentProgressService.getAssessmentAnswers(assessmentId);
		return ResponseHelper.createResponse(new LMSResponse<AssessmentAnswersActivity>(), responseDto,
				Translator.toLocale("assessment.answers.activity.retrieve.success", null),
				Translator.toLocale("assessment.answers.activity.retrieve.failed", null));
	}
}
