package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.enums.FileCategories;
import com.intelliread.enums.SubFolders;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.ResponseHelper;
import com.intelliread.services.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping(value = "/v1/api/file")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class FileUploadController {

	@Autowired
	private FileUploadService fileUploadService;

	@Lazy
	@SuppressWarnings("unchecked")
	@PostMapping("/upload")
	public LMSResponse<String> upload(@RequestParam(name = "fileCategory") FileCategories category,
			@RequestParam(name = "subFolders") SubFolders subFolders, @RequestParam(name = "file") MultipartFile file) {
		String response = fileUploadService.upload(category, subFolders, file);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("file.upload.success", null), Translator.toLocale("file.upload.fail", null));
	}
        
	@Lazy
	@SuppressWarnings("unchecked")
	@PostMapping("/upload-custom")
	public LMSResponse<String> uploadCustom(@RequestParam(name = "path") String path, @RequestParam(name = "file") MultipartFile file) {
		String response = fileUploadService.upload(path, file);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("file.upload.success", null), Translator.toLocale("file.upload.fail", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@PostMapping("/upload-multiple")
	public LMSResponse<List<String>> mulitpleUpload(@RequestParam(name = "fileCategory") FileCategories category,
			@RequestParam(name = "subFolders") SubFolders subFolders,
			@RequestParam(name = "files") MultipartFile[] files) {
		List<String> response = fileUploadService.mulitpleUpload(category, subFolders, files);
		return ResponseHelper.createResponse(new LMSResponse<List<String>>(), response,
				Translator.toLocale("files.upload.success", null), Translator.toLocale("files.upload.fail", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@DeleteMapping("/delete")
	public LMSResponse<String> delete(@RequestParam(name = "fileCategory") FileCategories category,
			@RequestParam(name = "subFolders") SubFolders subFolders,
			@RequestParam(name = "fileName") String fileName) {
		String response = fileUploadService.delete(category, subFolders, fileName);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("file.delete.success", null), Translator.toLocale("file.delete.fail", null));
	}
	

	@Lazy
	@SuppressWarnings("unchecked")
	@GetMapping("/read")
	public List<String> read(@RequestParam(name = "numberOfLine") int numberOfLine,
			@RequestParam(name = "fileType") String fileType) {
		String filePath = "";
		switch (fileType) {
	    case "content":
	      filePath = "/var/lib/jenkins/workspace/lms_backend_content_service/lms_backend_content_service/target/app.log";
	      break;
	    case "file-upload":
	      filePath = "/var/lib/jenkins/workspace/lms_backend_file_upload/lms_backend_file_upload/target/app.log";
	      break;
	    case "master":
	      filePath = "/var/lib/jenkins/workspace/lms_backend_master_service/lms_backend_master_service/target/app.log";
	      break;
	    case "notification":
	      filePath = "/var/lib/jenkins/workspace/lms_backend_notification/lms_backend_notification/target/app.log";
	      break;
	    case "user":
	      filePath = "/var/lib/jenkins/workspace/lms_backend_registration/lms_backend_registration/target/app.log";
	      break;
	    case "teacher":
	      filePath = "/var/lib/jenkins/workspace/lms_backend_teacher_service/lms_backend_teacher_service/target/app.log";
	      break;
	    case "student":
	      filePath = "/var/lib/jenkins/workspace/lms_backend_student_service/lms_backend_student_service/target/app.log";
	      break;
	    default:
	        log.info("Unknown fruit");
	  }
		File file = new File(filePath);
		try {
			return readLastNLines(file, numberOfLine);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return new ArrayList<String>();
		}
	}
	
	private List<String> readLastNLines(File file, int n) throws IOException {
        Deque<String> lines = new ArrayDeque<>(n);
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            long fileLength = raf.length();
            long position = fileLength - 1;
            raf.seek(position);
            
            StringBuilder line = new StringBuilder();
            for (long pointer = position; pointer >= 0; pointer--) {
                raf.seek(pointer);
                char c = (char) raf.readByte();
                
                if (c == '\n') {
                    if (line.length() > 0) {
                        lines.addFirst(line.reverse().toString());
                        line.setLength(0);
                        if (lines.size() > n) {
                            lines.removeLast();
                        }
                    }
                } else {
                    line.append(c);
                }
            }
            if (line.length() > 0) {
                lines.addFirst(line.reverse().toString());
                if (lines.size() > n) {
                    lines.removeLast();
                }
            }
        }
        return lines.stream().collect(Collectors.toList());
    }
	
	
	@Lazy
	@SuppressWarnings("unchecked")
	@PostMapping("/upload-custom/ir")
	public LMSResponse<String> uploadCustomIR(@RequestParam(name = "path") String path, @RequestParam(name = "file") MultipartFile file) {
		String response = fileUploadService.uploadCustomIR(path, file);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("file.upload.success", null), Translator.toLocale("file.upload.fail", null));
	}

}
