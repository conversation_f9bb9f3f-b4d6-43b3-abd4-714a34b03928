package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.entity.master.PaymentTransaction;
import com.intelliread.entity.master.StudentPaymentProfile;
import com.intelliread.enums.ErrorCodes;
import com.intelliread.exception.MSException;
import com.intelliread.request.dto.StudentPaymentProfileRequestDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.StudentPaymentProfileService;
import com.intelliread.utilities.ResponseHelper;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/v1/api/student-payment-profile")
public class StudentPaymentProfileController {

    @Autowired
    private StudentPaymentProfileService studentPaymentProfileService;

    @PutMapping("/{userId}")
    @SuppressWarnings("all")
    public LMSResponse<StudentPaymentProfile> updateStudentPaymentProfile(@Valid @RequestBody StudentPaymentProfileRequestDto profile, @PathVariable(value = "userId", required = true) String userId) {
        StudentPaymentProfile paymentProfile = studentPaymentProfileService.saveStudentPaymentProfile(profile, userId);

        return ResponseHelper.createResponse(new LMSResponse<StudentPaymentProfile>(), paymentProfile,
                Translator.toLocale("paymentprofile.create.success", null),
                Translator.toLocale("paymentprofile.create.failed", null));
    }

    @GetMapping("/{userId}")
    @SuppressWarnings("all")
    public LMSResponse<StudentPaymentProfile> getStudentPaymentProfile(@PathVariable(value = "userId", required = true) String userId) {
        StudentPaymentProfile paymentProfile = studentPaymentProfileService.getStudentPaymentProfile(userId);

        return ResponseHelper.createResponse(new LMSResponse<StudentPaymentProfile>(), paymentProfile,
                Translator.toLocale("paymentprofile.retrieve.success", null),
                Translator.toLocale("paymentprofile.retrieve.failed", null));
    }
}
