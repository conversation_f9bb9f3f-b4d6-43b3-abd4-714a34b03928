package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.LanguagesRequestDto;
import com.intelliread.response.dto.LanguageMappingResponseDto;
import com.intelliread.response.dto.LanguagesResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.LanguagesService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(value = "/v1/api/master/languages")
public class LanguagesController {

	@Autowired
	private LanguagesService languagesService;

	@PostMapping()
	@SuppressWarnings("unchecked")
	public LMSResponse<LanguagesResponseDto> createLanguages(@Valid @RequestBody LanguagesRequestDto request) {
		LanguagesResponseDto response = languagesService.createLanguages(request);
		return ResponseHelper.createResponse(new LMSResponse<LanguagesResponseDto>(), response,
				Translator.toLocale("languages.create.success", null),
				Translator.toLocale("languages.create.failed", null));
	}

	@PutMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<LanguagesResponseDto> updateLanguages(@PathVariable("id") String id,
			@Valid @RequestBody LanguagesRequestDto request) {
		LanguagesResponseDto response = languagesService.updateLanguages(id, request);
		return ResponseHelper.createResponse(new LMSResponse<LanguagesResponseDto>(), response,
				Translator.toLocale("languages.update.success", null),
				Translator.toLocale("languages.update.failed", null));
	}

	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<LanguagesResponseDto> getLanguagesById(@PathVariable("id") String id) {
		LanguagesResponseDto response = languagesService.getLanguagesById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<LanguagesResponseDto>(), response,
				Translator.toLocale("languages.get.detail.success", null),
				Translator.toLocale("languages.get.detail.failed", null));
	}

	@GetMapping("/all")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<LanguagesResponseDto>> getAllLanguages() {
		List<LanguagesResponseDto> response = languagesService.getAllLanguages();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<LanguagesResponseDto>>(), response,
				Translator.toLocale("languages.get.all.success", null),
				Translator.toLocale("languages.get.all.failed", null));
	}

	@GetMapping("/all-by-ids")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<LanguagesResponseDto>> getLanguagesByIds(@RequestParam("ids") List<String> ids) {
		List<LanguagesResponseDto> response = languagesService.getLanguageByIds(ids);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<LanguagesResponseDto>>(), response,
				Translator.toLocale("languages.get.all.success", null),
				Translator.toLocale("languages.get.all.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> deleteLanguagesById(@PathVariable("id") String id) {
		Boolean response = languagesService.deleteLanguagesById(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("languages.deleted.success", null),
				Translator.toLocale("languages.deleted.failed", null));
	}
	
	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = languagesService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/{id}/mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<LanguageMappingResponseDto> getAllLanguageMappings(@PathVariable("id") String id) {
		LanguageMappingResponseDto response = languagesService.getAllLanguageMappings(id);
		return ResponseHelper.createResponse(new LMSResponse<LanguageMappingResponseDto>(), response,
				Translator.toLocale("languages.mapping.fetch.success", null),
				Translator.toLocale("languages.mapping.fetch.failed", null));
	}
}
