package com.intelliread.controller;

import com.intelliread.entity.master.UserPerformanceReport;
import com.intelliread.response.ApiResponse;
import com.intelliread.response.Pagination;
import com.intelliread.services.impl.UserPerformanceReportServiceImpl;
import com.intelliread.utilities.NumberUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/api/performance-report")
public class UserPerformanceReportController {

    @Autowired
    private UserPerformanceReportServiceImpl performanceReportService;

    @GetMapping("/users/{userId}")
    public ResponseEntity<?> getUserReports(@PathVariable String userId, @RequestParam(required = false) List<String> fields, HttpServletRequest request) {
        int page = NumberUtils.parseIntWithDefault(request.getParameter("page"), 0);
        int size = NumberUtils.parseIntWithDefault(request.getParameter("limit"), 5);

        if (page > 0) {
            page--;
        }

        Page<?> reports = performanceReportService.getUserReportsWithFields(userId, fields, page, size);
        Pagination pagination = new Pagination(
                reports.getNumber() + 1,  // current page (1-based)
                reports.getTotalPages(),
                reports.getTotalElements(),
                reports.getSize()
        );

        return ResponseEntity.ok().body(new ApiResponse<>(reports.getContent(), pagination));
    }

    @GetMapping("/users/{userId}/course/{courseId}")
    public ResponseEntity<?> getReportByCourseAndUserId(@PathVariable String userId, @PathVariable String courseId, HttpServletRequest request) {
        String chapterId = request.getParameter("chapter_id");
        String sectionId = request.getParameter("section_id");
        int page = NumberUtils.parseIntWithDefault(request.getParameter("page"), 0);
        int size = NumberUtils.parseIntWithDefault(request.getParameter("limit"), 5);

        if (page > 0) {
            page--;
        }

        Page<UserPerformanceReport> report = performanceReportService.getReportByUserAndCourse(courseId, userId, page, size);

        List<UserPerformanceReport> reports = report.getContent();
        Pagination pagination = new Pagination(
                report.getNumber() + 1,  // current page (1-based)
                report.getTotalPages(),
                report.getTotalElements(),
                report.getSize()
        );

        return ResponseEntity.ok().body(new ApiResponse<>(reports, pagination));
    }
}
