package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.ActivitiesRequestDto;
import com.intelliread.request.dto.AddAssessmentSummaryRequest;
import com.intelliread.request.dto.AddProgressSummaryRequest;
import com.intelliread.request.dto.AssignmentProgressRequestDto;
import com.intelliread.response.dto.*;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.AssignmentProgressService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping(value = "/v1/api/master/assignmentProgress")
public class AssignmentProgressController {
	
	@Autowired
	private AssignmentProgressService assignmentProgressService;

	@PostMapping()
	@SuppressWarnings("all")
	public LMSResponse<AssignmentProgressResponse> createAssignmentProgress(@Valid @RequestBody AssignmentProgressRequestDto request) {
		AssignmentProgressResponse response = assignmentProgressService.createAssignmentProgress(request);
		return ResponseHelper.createResponse(new LMSResponse<AssignmentProgressResponse>(), response,
				Translator.toLocale("assignmentprogress.created.success", null),
				Translator.toLocale("assignmentprogress.create.failed", null));
	}
	
	@PutMapping("/activity/{id}")
	@SuppressWarnings("all")
	public LMSResponse<ActivitiesResponse> addActivities(@Valid @PathVariable ("id") String id,@Valid @RequestBody ActivitiesRequestDto request) {
		ActivitiesResponse response = assignmentProgressService.addActivities(id,request);
		return ResponseHelper.createResponse(new LMSResponse<ActivitiesResponse>(), response,
				Translator.toLocale("activities.added.success", null),
				Translator.toLocale("activities.added.failed", null));
	}
	
	@PutMapping("/activity/{courseId}/{assigneeId}")
	@SuppressWarnings("all")
	public LMSResponse<ActivitiesResponse> addActivitiesByCourseAndAssigneeId(@Valid @PathVariable ("courseId") String courseId,@Valid @PathVariable ("assigneeId") String assigneeId,@Valid @RequestBody ActivitiesRequestDto request) {
		ActivitiesResponse response = assignmentProgressService.addActivitiesByCourseAndAssigneeId(courseId,assigneeId,request);
		return ResponseHelper.createResponse(new LMSResponse<ActivitiesResponse>(), response,
				Translator.toLocale("activities.added.success", null),
				Translator.toLocale("activities.added.failed", null));
	}
	
	@PutMapping("/progressSummary/{id}")
	@SuppressWarnings("all")
	public LMSResponse<AddProgressSummaryResponse> addProgressSummary(@Valid @PathVariable ("id") String id,@Valid @RequestBody AddProgressSummaryRequest request) {
		AddProgressSummaryResponse response = assignmentProgressService.addProgressSummary(id,request);
		return ResponseHelper.createResponse(new LMSResponse<AddProgressSummaryResponse>(), response,
				Translator.toLocale("progresssummary.added.success", null),
				Translator.toLocale("progresssummary.added.failed", null));
	}
	
	@PutMapping("/progressSummary/{courseId}/{assigneeId}")
	@SuppressWarnings("all")
	public LMSResponse<AddProgressSummaryResponse> addProgressSummaryByCourseandAssigneeId(@Valid @PathVariable ("courseId") String courseId,@Valid @PathVariable ("assigneeId") String assigneeId,@Valid @RequestBody AddProgressSummaryRequest request) {
		AddProgressSummaryResponse response = assignmentProgressService.addProgressSummaryByCourseandAssigneeId(courseId,assigneeId,request);
		return ResponseHelper.createResponse(new LMSResponse<AddProgressSummaryResponse>(), response,
				Translator.toLocale("progresssummary.added.success", null),
				Translator.toLocale("progresssummary.added.failed", null));
	}
	
	@PutMapping("/assessmentSummary/{id}")
	@SuppressWarnings("all")
	public LMSResponse<AddAssessmentSummaryRespone> addAssessmentSummary(@Valid @PathVariable ("id") String id,@Valid @RequestBody AddAssessmentSummaryRequest request) {
		AddAssessmentSummaryRespone response = assignmentProgressService.addAssessmentSummary(id,request);
		return ResponseHelper.createResponse(new LMSResponse<AddAssessmentSummaryRespone>(), response,
				Translator.toLocale("assessmentsummary.added.success", null),
				Translator.toLocale("assessmentsummary.added.success", null));
	}
	
	@PutMapping("/assessmentSummary/{courseId}/{assigneeId}")
	@SuppressWarnings("all")
	public LMSResponse<AddAssessmentSummaryRespone> addAssessmentSummaryByCourseAndAssigneeId(@Valid @PathVariable ("courseId") String courseId,@Valid @PathVariable ("assigneeId") String assigneeId,@Valid @RequestBody AddAssessmentSummaryRequest request) {
		AddAssessmentSummaryRespone response = assignmentProgressService.addAssessmentSummaryByCourseAndAssigneeId(courseId,assigneeId,request);
		return ResponseHelper.createResponse(new LMSResponse<AddAssessmentSummaryRespone>(), response,
				Translator.toLocale("assessmentsummary.added.success", null),
				Translator.toLocale("assessmentsummary.added.success", null));
	}
	
	
	@GetMapping("/activity/{id}")
	@SuppressWarnings("all")
	public LMSResponse<ActivityResponse> getActivities(@Valid @PathVariable ("id") String id) {
		ActivityResponse response = assignmentProgressService.getActivities(id);
		return ResponseHelper.createResponse(new LMSResponse<ActivityResponse>(), response,
				Translator.toLocale("activities.retrieved.success", null),
				Translator.toLocale("activities.retrieve.failed", null));
	}
	
	@GetMapping("/activity/{id}/{assigneeId}")
	@SuppressWarnings("all")
	public LMSResponse<ActivityResponse> getActivitiesByCourseAndAssigneeId(@Valid @PathVariable ("id") String id,@Valid @PathVariable ("assigneeId") String assigneeId) {
		ActivityResponse response = assignmentProgressService.getActivitiesByCourseAndAssigneeId(id,assigneeId);
		return ResponseHelper.createResponse(new LMSResponse<ActivityResponse>(), response,
				Translator.toLocale("activities.retrieved.success", null),
				Translator.toLocale("activities.retrieve.failed", null));
	}
	
	@SuppressWarnings("all")
	@GetMapping("/activity/pagination")
	public LMSResponse<ActivityCreateResponse> getSortedActivityPagination(
			  @RequestParam(value = "pageNo", defaultValue = "0", required = true)@Min(0) int pageNo,
	            @RequestParam(value = "pageSize", defaultValue = "10", required = true)@Min(1) @Max(50) int pageSize,
				@RequestParam(value = "courseId", required = false) String courseId,
				@RequestParam(value = "assigneeId", required = false) String assigneeId
	) {
		ActivityCreateResponse response=assignmentProgressService.getSortedActivityPagination(pageNo, pageSize,courseId,assigneeId);
    	return ResponseHelper.responseForGetOrFeign(new LMSResponse<ActivityCreateResponse>(), response,
    			Translator.toLocale("activities.retrieved.success", null),
				Translator.toLocale("activities.retrieve.failed", null));
	}
	
	

	
	@GetMapping("/progressSummary/{id}")
	@SuppressWarnings("all")
	public LMSResponse<ProgressSummaryResponse> getProgressSummary(@Valid @PathVariable ("id") String id) {
		ProgressSummaryResponse response = assignmentProgressService.getProgressSummary(id);
		return ResponseHelper.createResponse(new LMSResponse<ProgressSummaryResponse>(), response,
				Translator.toLocale("progresssummary.retrieved.success", null),
				Translator.toLocale("progresssummary.retrieve.failed", null));
	}
	
	@GetMapping("/progressSummary/{id}/{assigneeId}")
	@SuppressWarnings("all")
	public LMSResponse<ProgressSummaryResponse> getProgressSummaryByCourseAndAssigneeId(@Valid @PathVariable ("id") String id,@Valid @PathVariable ("assigneeId") String assigneeId) {
		ProgressSummaryResponse response = assignmentProgressService.getProgressSummaryByCourseAndAssigneeId(id,assigneeId);
		return ResponseHelper.createResponse(new LMSResponse<ProgressSummaryResponse>(), response,
				Translator.toLocale("progresssummary.retrieved.success", null),
				Translator.toLocale("progresssummary.retrieve.failed", null));
	}
	
	@SuppressWarnings("all")
	@GetMapping("/progressSummary/pagination")
	public LMSResponse<ProgressSummaryCreate> getProgressSummaryByPagination(
			  @RequestParam(value = "pageNo", defaultValue = "0", required = true)@Min(0) int pageNo,
	            @RequestParam(value = "pageSize", defaultValue = "10", required = true)@Min(1) @Max(50) int pageSize,
				@RequestParam(value = "courseId", required = false) String courseId,
				@RequestParam(value = "assigneeId", required = false) String assigneeId
	) {
		ProgressSummaryCreate response=assignmentProgressService.getProgressSummaryByPagination(pageNo, pageSize,courseId,assigneeId);
    	return ResponseHelper.responseForGetOrFeign(new LMSResponse<ProgressSummaryCreate>(), response,
    			Translator.toLocale("progresssummary.retrieved.success", null),
				Translator.toLocale("progresssummary.retrieve.failed", null));
	}
	
	@GetMapping("/assessmentSummary/{id}")
	@SuppressWarnings("all")
	public LMSResponse<AssessmentSummaryResponse> getAssessmentSummary(@Valid @PathVariable ("id") String id) {
		AssessmentSummaryResponse response = assignmentProgressService.getAssessmentSummary(id);
		return ResponseHelper.createResponse(new LMSResponse<AssessmentSummaryResponse>(), response,
				Translator.toLocale("assessmentsummary.retrieved.success", null),
				Translator.toLocale("assessmentsummary.retrieve.failed", null));
	}
	
	@GetMapping("/assessmentSummary/{id}/{assigneeId}")
	@SuppressWarnings("all")
	public LMSResponse<AssessmentSummaryResponse> getAssessmentSummaryByCourseAndAssigneeIds(@Valid @PathVariable ("id") String id,@Valid @PathVariable ("assigneeId") String assigneeId) {
		AssessmentSummaryResponse response = assignmentProgressService.getAssessmentSummaryByCourseAndAssigneeIds(id,assigneeId);
		return ResponseHelper.createResponse(new LMSResponse<AssessmentSummaryResponse>(), response,
				Translator.toLocale("assessmentsummary.retrieved.success", null),
				Translator.toLocale("assessmentsummary.retrieve.failed", null));
	}
	
	@SuppressWarnings("all")
	@GetMapping("/assessmentSummary/pagination")
	public LMSResponse<AssessmentSummaryCreate> getAssessmentSummaryByPagination(
			  @RequestParam(value = "pageNo", defaultValue = "0", required = true)@Min(0) int pageNo,
	            @RequestParam(value = "pageSize", defaultValue = "10", required = true)@Min(1) @Max(50) int pageSize,
				@RequestParam(value = "courseId", required = false) String courseId,
				@RequestParam(value = "assigneeId", required = false) String assigneeId
	) {
		AssessmentSummaryCreate response=assignmentProgressService.getAssessmentSummaryByPagination(pageNo, pageSize,courseId,assigneeId);
    	return ResponseHelper.responseForGetOrFeign(new LMSResponse<AssessmentSummaryCreate>(), response,
    			Translator.toLocale("assessmentsummary.retrieved.success", null),
				Translator.toLocale("assessmentsummary.retrieve.failed", null));
	}
	
	@GetMapping("/{courseId}/{assigneeId}")
	@SuppressWarnings("all")
	public LMSResponse<CourseAssignmentProgress> getAssignmentProgress(@Valid @PathVariable ("courseId") String courseId,@Valid @PathVariable ("assigneeId") String assigneeId) {
		CourseAssignmentProgress response = assignmentProgressService.getAssignmentProgress(courseId,assigneeId);
		return ResponseHelper.createResponse(new LMSResponse<CourseAssignmentProgress>(), response,
				Translator.toLocale("assignmentprogress.retrived.success", null),
				Translator.toLocale("assignmentprogress.retrive.failed", null));
	}
	
	@PutMapping("/update/activity/{courseId}/{assigneeId}")
	@SuppressWarnings("all")
	public LMSResponse<ActivitiesResponse> addOrAppendActivitiesByCourseAndAssigneeId(@Valid @PathVariable ("courseId") String courseId,@Valid @PathVariable ("assigneeId") String assigneeId,@Valid @RequestBody ActivitiesRequestDto request) {
		ActivitiesResponse response = assignmentProgressService.addOrAppendActivitiesByCourseAndAssigneeId(courseId,assigneeId,request);
		return ResponseHelper.createResponse(new LMSResponse<ActivitiesResponse>(), response,
				Translator.toLocale("activities.added.success", null),
				Translator.toLocale("activities.added.failed", null));
	}
	
	
	

}
