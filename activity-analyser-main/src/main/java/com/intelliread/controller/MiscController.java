package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.response.dto.EnumsResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.ResponseHelper;
import com.intelliread.services.MiscService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/v1/api/file/misc")
public class MiscController {

	@Autowired
	private MiscService miscService;

	@Lazy
	@SuppressWarnings("unchecked")
	@GetMapping("/file-categories")
	public LMSResponse<List<EnumsResponseDto>> listOfFileCategories() {
		List<EnumsResponseDto> response = miscService.listOfFileCategories();
		return ResponseHelper.createResponse(new LMSResponse<List<EnumsResponseDto>>(), response,
				Translator.toLocale("files.category.success", null), Translator.toLocale("files.category.fail", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@GetMapping("/sub-folders")
	public LMSResponse<List<EnumsResponseDto>> listOfSubFolders() {
		List<EnumsResponseDto> response = miscService.listOfSubFolders();
		return ResponseHelper.createResponse(new LMSResponse<List<EnumsResponseDto>>(), response,
				Translator.toLocale("sub.folder.enum.success", null),
				Translator.toLocale("sub.folder.enum.fail", null));
	}
}
