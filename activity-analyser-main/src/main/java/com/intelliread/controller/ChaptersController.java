package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.request.dto.ChaptersRequestDto;
import com.intelliread.response.dto.GetIrChaptersList;
import com.intelliread.response.dto.IrChaptersResponseDto;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.ChaptersService;
import com.intelliread.utilities.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping(value = "/v1/api/irchapters")
public class ChaptersController {
	
	@Autowired
	ChaptersService chaptersService;
	
	
	@PostMapping()
	@SuppressWarnings("all")
	public LMSResponse<IrChaptersResponseDto> createIrChapters(@Valid @RequestBody ChaptersRequestDto dto){
		IrChaptersResponseDto response= chaptersService.createIrChapters(dto);
		return  ResponseHelper.createResponse(new LMSResponse<IrChaptersResponseDto>(), response,
				Translator.toLocale("irchapters.created.success", null),
				Translator.toLocale("irchapters.create.failed", null));
	}
	
	@GetMapping()
	@SuppressWarnings("all")
	public LMSResponse<GetIrChaptersList> getChapters(){
		GetIrChaptersList response= chaptersService.getChapters();
		return  ResponseHelper.createResponse(new LMSResponse<GetIrChaptersList>(), response,
				Translator.toLocale("irchapters.retrieved.success", null),
				Translator.toLocale("irchapters.retrieve.failed", null));
	}

}
