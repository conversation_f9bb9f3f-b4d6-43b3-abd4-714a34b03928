package com.intelliread.controller;

import com.intelliread.component.Translator;
import com.intelliread.entity.master.PaymentTransaction;
import com.intelliread.repository.master.PaymentTransactionRepository;
import com.intelliread.request.dto.SendPaymentLinkRequestDto;
import com.intelliread.response.dto.DeleteCourse;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.services.PaymentUserService;
import com.intelliread.utilities.ResponseHelper;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/v1/api/user-payments")
public class PaymentUserController {

    @Autowired
    private PaymentUserService paymentUserService;

    @Autowired
    private PaymentTransactionRepository paymentTransactionRepository;

    @GetMapping("/pending/{userId}")
    @SuppressWarnings("all")
    public LMSResponse<List<PaymentTransaction>> getUsersWithPendingPayments(@PathVariable(required = true, value = "userId") String userId) {
        List<PaymentTransaction> transactions = paymentUserService.hasPendingPayments(userId);

        return ResponseHelper.createResponse(new LMSResponse<>(), transactions,
                Translator.toLocale("payment.user.pendingtransactions.list.success", null),
                Translator.toLocale("payment.user.pendingtransactions.list.failed", null));
    }

    @PostMapping("/send-payment-link")
    @SuppressWarnings("all")
    public LMSResponse<List<PaymentTransaction>> sendPaymentLink(@Valid @RequestBody SendPaymentLinkRequestDto requestDto) {
        paymentUserService.sendPaymentLinkByUserAndTransactionId(requestDto);

        Map<String, String> response = new HashMap<>();
        response.put("status", "sent");

        return ResponseHelper.createResponse(new LMSResponse<>(), response,
                Translator.toLocale("payment.email.sent.success", null),
                Translator.toLocale("payment.email.sent.failed", null));
    }

    @PostMapping("/mark-as-paid")
    @SuppressWarnings("all")
    public LMSResponse<List<PaymentTransaction>> markCashPaymentsAsComplete(@Valid @RequestBody SendPaymentLinkRequestDto requestDto) {
        paymentUserService.markPaymentAsCompleteByUserAndTransactionId(requestDto);

        Map<String, String> response = new HashMap<>();
        response.put("status", "paid");

        return ResponseHelper.createResponse(new LMSResponse<>(), response,
                Translator.toLocale("payment.success.callback.complete", null),
                Translator.toLocale("payment.success.callback.failed", null));
    }
}
