package com.intelliread.config;

import com.intelliread.component.Translator;
import com.intelliread.enums.ErrorCodes;
import com.intelliread.exception.MSException;
import com.intelliread.repository.master.ConfigurationTableRepository;
import com.intelliread.response.dto.ConfigurationTableResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import java.util.List;

/**
 * Load the details from the configuration_table and save to a permanent file.
 * Loading will happen during the application deploying
 * 
 * <AUTHOR> C A<PERSON>ri
 * 
 */
@Slf4j
//@Configuration
public class LoadConfigurations implements ApplicationRunner {

	private ConfigurationTableRepository configRepository;
	
	@Autowired
	public LoadConfigurations(ConfigurationTableRepository configRepository) {
		this.configRepository = configRepository;
	}

	@Override
	public void run(ApplicationArguments args) throws Exception {
		getAllTheConfiguration();
	}
	
	public List<ConfigurationTableResponseDto> getAllTheConfiguration() {
		try {
			return configRepository.findAllConfiguration();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new MSException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("configuration.fetch.all.failed", null));
		}
	}
}
