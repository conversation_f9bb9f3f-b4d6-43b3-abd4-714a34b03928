package com.intelliread.config;

import feign.Client;
import feign.Logger;
import feign.Request;
import feign.RequestInterceptor;
import feign.codec.ErrorDecoder;
import feign.httpclient.ApacheHttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

@Configuration
public class FiegnConfiguration {

	@Bean
	public ErrorDecoder errorDecoder() {
		return new CustomErrorDecoder();
	}

	@Bean
	public Logger.Level feignLoggerLevel() {
		return Logger.Level.FULL;
	}

	@Bean
	public RequestInterceptor FeignRequestInterceptor() {
		return new FeignClientInterceptor();
	}

	@Bean
	public PoolingHttpClientConnectionManager poolingConnectionManager() {
		PoolingHttpClientConnectionManager poolingConnectionManager = new PoolingHttpClientConnectionManager();
		poolingConnectionManager.setMaxTotal(4000);
		poolingConnectionManager.setDefaultMaxPerRoute(1000);
		return poolingConnectionManager;
	}

	@Bean
	@Primary
	public CloseableHttpClient httpClient(PoolingHttpClientConnectionManager poolingConnectionManager) {
		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(60000).setSocketTimeout(60000).build();
		return HttpClients.custom().setConnectionManager(poolingConnectionManager)
				.setDefaultRequestConfig(requestConfig).build();
	}

	@Bean
	public Client feignClient(CloseableHttpClient httpClient) {
		return new ApacheHttpClient(httpClient);
	}

	@Bean
	public ApacheHttpClient apacheHttpClient(CloseableHttpClient httpClient) {
		return new ApacheHttpClient(httpClient);
	}

	@Bean
	public Request.Options requestOptions() {
		return new Request.Options(5, TimeUnit.SECONDS, 60, TimeUnit.SECONDS, true);
	}
}
