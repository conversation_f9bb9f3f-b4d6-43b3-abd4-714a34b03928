package com.intelliread.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitMQConfig {
    public static final String QUEUE_NAME = "gradingQueue";
    public static final String EXCHANGE_NAME = "gradingExchange";
    public static final String ROUTING_KEY = "grading.key";

    @Bean
    public Queue gradingQueue() {
        return new Queue(QUEUE_NAME, true); // Durable queue
    }

    @Bean
    public TopicExchange gradingExchange() {
        return new TopicExchange(EXCHANGE_NAME);
    }

    @Bean
    public Binding gradingBinding(Queue gradingQueue, TopicExchange gradingExchange) {
        return BindingBuilder.bind(gradingQueue).to(gradingExchange).with(ROUTING_KEY);
    }

    @Bean
    public Jackson2JsonMessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(messageConverter());
        return rabbitTemplate;
    }
}
