package com.intelliread.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;

import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import jakarta.persistence.EntityManagerFactory;

/**
 * Configuration for the primary (master) data source, entity manager, and transaction manager.
 *<p>
 * Sets up connection and management for entities under "com.intelliread.entity.master". <br>
 * - Configures {@code masterDataSource} using properties prefixed with "spring.datasource.primary".<br>
 * - Defines {@code masterEntityManagerFactory} for managing master database entities.<br>
 * - Provides {@code masterTransactionManager} for handling transactions on the master database.<br>
 *</p>
 * <p>
 * Annotations:<br>
 * - {@link Configuration}: Declares this as a configuration class.<br>
 * - {@link EnableTransactionManagement}: Enables transaction management.<br>
 * - {@link EnableJpaRepositories}: Configures JPA repositories for the master database.<br>
 *</p>
 * The {@code @Primary} annotation ensures this is the default data source when multiple are defined.
 *
 * <AUTHOR> Mandal
 * @since 1.0.1
 */

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.intelliread.repository.master",
        entityManagerFactoryRef = "masterEntityManagerFactory",
        transactionManagerRef = "masterTransactionManager"
)
public class MasterDataSourceConfig {

    @Bean(name = "masterDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.primary")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "masterEntityManagerFactory")
    @Primary
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder, @Qualifier("masterDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.intelliread.entity.master")
                .persistenceUnit("master")
                .build();
    }

    @Bean(name = "masterTransactionManager")
    @Primary
    public PlatformTransactionManager transactionManager(
            @Qualifier("masterEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
