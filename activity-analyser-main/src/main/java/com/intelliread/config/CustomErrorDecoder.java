package com.intelliread.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.intelliread.component.Translator;
import com.intelliread.enums.ErrorCodes;
import com.intelliread.exception.MSException;
import com.intelliread.response.models.LMSResponse;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;

import java.io.IOException;

@Slf4j
public class CustomErrorDecoder implements ErrorDecoder {

	@Autowired
	private ObjectMapper objectMapper;

	public Exception decode(String methodKey, Response response) {
		try {
			int status = response.status();
			if (status != -1 && response.body() != null && response.body().asInputStream() != null) {
				LMSResponse<?> errorResponse = objectMapper.readValue(
						response.body().asInputStream(),
						new TypeReference<LMSResponse<Object>>() {}
				);

				if (status == HttpStatus.NOT_FOUND.value()) {
					throw new MSException(ErrorCodes.NOT_FOUND, errorResponse.getMessage());
				} else if (status == HttpStatus.FORBIDDEN.value()) {
					throw new MSException(ErrorCodes.ACCESS_DENIED, errorResponse.getMessage());
				} else if (status == HttpStatus.UNAUTHORIZED.value()) {
					throw new MSException(ErrorCodes.UNAUTHORIZED, errorResponse.getMessage());
				} else if (status == HttpStatus.BAD_REQUEST.value()) {
					throw new MSException(ErrorCodes.BAD_REQUEST, errorResponse.getMessage());
				} else if (status == HttpStatus.INTERNAL_SERVER_ERROR.value()) {
					throw new MSException(ErrorCodes.INTERNAL_SERVER_ERROR, errorResponse.getMessage());
				} else if (status == HttpStatus.CONFLICT.value()) {
					throw new MSException(ErrorCodes.CONFLICT, errorResponse.getMessage());
				} else {
					throw new MSException(ErrorCodes.INTERNAL_SERVER_ERROR, errorResponse.getMessage());
				}
			}
		} catch (IOException ioe) {
			log.error(ExceptionUtils.getStackTrace(ioe));
		}
		throw new MSException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("internal.server.error", null));
	}
}
