package com.intelliread.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;

import javax.sql.DataSource;
import jakarta.persistence.EntityManagerFactory;

/**
 * Configuration for the users data source, entity manager, and transaction manager.
 * <p>
 * Sets up connection and management for entities under "com.intelliread.entity.users". <br>
 * - Configures {@code usersDataSource} using properties prefixed with "spring.datasource.users".<br>
 * - Defines {@code usersEntityManagerFactory} for managing user database entities.<br>
 * - Provides {@code usersTransactionManager} for handling transactions on the users database.<br>
 * </p>
 * <p>
 * Annotations:<br>
 * - {@link Configuration}: Declares this as a configuration class.<br>
 * - {@link EnableTransactionManagement}: Enables transaction management.<br>
 * - {@link EnableJpaRepositories}: Configures JPA repositories for the users database.<br>
 * </p>
 * This class sets up the users database as a separate data source and transaction manager,
 * allowing seamless integration of entities and repositories associated with user data.
 *
 * <AUTHOR> Mandal
 * @since 1.0.1
 */

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.intelliread.repository.users",  // All users db repositories
        entityManagerFactoryRef = "usersEntityManagerFactory",
        transactionManagerRef = "usersTransactionManager"
)
public class UsersDataSourceConfig {

    @Bean(name = "usersDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.users")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "usersEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder, @Qualifier("usersDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.intelliread.entity.users")
                .persistenceUnit("users")
                .build();
    }

    @Bean(name = "usersTransactionManager")
    public PlatformTransactionManager transactionManager(
            @Qualifier("usersEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
