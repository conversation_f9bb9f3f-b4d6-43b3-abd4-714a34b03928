package com.intelliread.config;

import org.springframework.lang.NonNull;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;

public class LocaleResolver implements org.springframework.web.servlet.LocaleResolver {
    private static final List<Locale> LOCALES = Arrays.asList(Locale.of("en"), Locale.of("fr"));

    @Override
    public @NonNull Locale resolveLocale(HttpServletRequest request) {
        String headerLang = request.getHeader("Accept-Language");
        return (headerLang == null || headerLang.isEmpty())
                ? Locale.getDefault()
                : Locale.lookup(Locale.LanguageRange.parse(headerLang), LOCALES);
    }

    @Override
    public void setLocale(@NonNull HttpServletRequest request, HttpServletResponse response, Locale locale) {

    }
}
