package com.intelliread.exception;

import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;

/**
 * Create a custom exceptional handler, customised exceptional model used
 * instead of the ResponseEntity.class
 * 
 * <AUTHOR>
 * @since 1.1.1
 *
 */
@ControllerAdvice
@RestController
public class FUServiceExceptionHandler {
	
	/**
	 * Returns custom error response on occurrence of custom exception.
	 * 
	 * @param e
	 * @param response
	 * @return
	 */
	@ExceptionHandler(value = FUException.class)
	public ErrorResponse handleContentNotFoundException(FUException e, HttpServletResponse response) {
		response.setStatus(e.getErrorCode());
		ErrorResponse error = new ErrorResponse();
		error.setMessage(e.getMessage());
		error.setErrorCode(e.getErrorCode());
		error.setSuccess(false);
		return error;
	}
}
