package com.intelliread.exception;

import com.intelliread.enums.ErrorCodes;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 0.0.1
 *
 */
@Getter
@Setter
public class MSException extends RuntimeException {

	private static final long serialVersionUID = 1L;

	private Integer errorCode;

	private String message;
	
	public MSException(String message) {
		super(message);
		this.message = message;
	}
	
	public MSException(Integer errorCode, String message) {
		super(message);
		this.errorCode = errorCode;
		this.message = message;
	}
	
	public MSException(ErrorCodes errorCode, String message) {
		super(message);
		this.errorCode = errorCode.getCode();
		this.message = message;
	}
	
	public MSException(Integer errorCode, Exception ex) {
		super(ex);
		this.errorCode = errorCode;
		this.message = ex.getMessage();
	}
	
	public MSException(Exception ex) {
		super(ex);
		this.message = ex.getMessage();
	}

}
