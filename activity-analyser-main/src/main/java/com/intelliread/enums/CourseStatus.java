package com.intelliread.enums;

public enum CourseStatus {

	ACTIVE("ACTIVE", "Active"), INACTIVE("INACTIVE", "Inactive"), ARCHIVE("ARCHIVE", "Archive"),
	DRAFT("DRAFT", "Draft"), ON_HOLD("ON_HOLD", "OnHold"),UNPUBLISHED("UNPUBLISHED","UnPublished"),
	PUBLISHED("PUBLISHED","Published"),INREVIEW("INREVIEW","InReview"),APPROVED("APPROVED","Approved"),
	REJECTED("REJECTED","Rejected"),COMPLETED("COMPLETED","Completed");

	String code;

	String name;

	CourseStatus(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

}