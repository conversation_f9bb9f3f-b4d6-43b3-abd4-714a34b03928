package com.intelliread.enums;

/**
 * Plan template columns
 * 
 * <AUTHOR>
 *
 */
public enum PlanFeatures {

	LESSON_PLAN("lesson_plan", "LESSON_PLAN"),
	TEACHER_REVISION_MODULE("teacher_revision_module", "TEACHER_REVISION_MODULE"), RM_LOCK("rm_lock", "RM_LOCK"),
	ACTIVITIES("activities", "ACTIVITIES"), ASSESSMENT_MODULE("assessment_module", "ASSESSMENT_MODULE"),
	WB_DOWNLOAD("wb_download", "WB_DOWNLOAD"), WS_DOWNLOAD("ws_download", "WS_DOWNLOAD"),
	NEWS_STORIES("news_stories", "NEWS_STORIES"), STUDENT_BOOKLET("student_booklet", "STUDENT_BOOKLET"),
	STUDENT_REVISION_MODULE("student_revision_module", "STUDENT_REVISION_MODULE");

	String code;

	String name;

	PlanFeatures(String name, String code) {
		this.name = name;
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

}
