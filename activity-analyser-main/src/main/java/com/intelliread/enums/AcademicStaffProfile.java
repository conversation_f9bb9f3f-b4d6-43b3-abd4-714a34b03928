package com.intelliread.enums;

public enum AcademicStaffProfile {

	TEA<PERSON><PERSON>("Teacher", "TEACHER"), 
	COORDI<PERSON>TOR("Coordinator", "COORDINATOR"), 
	PRINCIPAL("Principal", "PRINCIPAL");

	private String name;

	private String code;

	AcademicStaffProfile(String name, String code) {
		this.name = name;
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}
}
