package com.intelliread.enums;

public enum TypeAssignmentEnum {
	LISTEN("LISTEN", "LISTEN"), SPEAK_ALOUD("SPEAK_ALOUD", "SPEAK_ALOUD"),
	ASSESSMENT("ASSESSMENT", "ASSESSMENT");

	private String name;

	private String code;

	TypeAssignmentEnum(String name, String code) {
		this.name = name;
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}
}
