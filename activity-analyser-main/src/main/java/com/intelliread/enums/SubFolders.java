package com.intelliread.enums;

/**
 * Add the sub folder's name here and update the array list in the class
 * {@link com.intelliread.utilities.MainAndSubFolders MainAndSubFolders}'s method
 * {@link com.intelliread.utilities.MainAndSubFolders
 * createMainOrSubFolder(String mainFolder, String subFolder)}
 * List name subFolders 
 * 
 * <AUTHOR>
 * @since 1.0.0
 *
 */
public enum SubFolders {
	
	//sub-folders under the folder Content-Service
	BLUE_PRINT("BLUE_PRINT", "Blue-Print"),
	DOCUMENT_CATERGORY("DOCUMENT_CATERGORY", "Document-Category"),
	INTERACTIVE_CONTENT("INTERACTIVE_CONTENT", "Interactive-Content"), 
	COMMON_CONTENT("COMMON_CONTENT", "Common-Content"),
	CHAPTERS("CHAPTERS", "Chapters"),
	QUESTIONS("QUESTIONS", "Questions"),
	QUIZZES("QUIZZES", "Quizzes"),
	
	
	//sub-folders under the folder Message-Service
	CHAT_MEDIA("CHAT_MEDIA", "Chat-Media"),
        
	//sub-folders under the folder User-Service
	SCHOOL("SCHOOL", "School"),
	BRANCH("BRANCH", "Branch"),
	STAFF("STAFF", "Staff"),
	STUDENT("STUDENT", "Student"),
	COURSES("COURSES", "Courses");
	
	private String code;
	
	private String name;
	
	SubFolders(String code, String name) {
		this.code = code;
		this.name = name;
	}
	
	public String getCode() {
		return code;
	}
	
	public String getName() {
		return name;
	}
}
