package com.intelliread.enums;

public enum PlanTemplateViewType {

	MENU("Menu", "MENU"), SUB_MENU("Sub Menu", "SUB_MENU"), DOCUMENT_CATEGORY("Document Category", "DOCUMENT_CATEGORY"),
	BUTTON("Button", "BUTTON");

	String code;

	String name;

	PlanTemplateViewType(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}
}
