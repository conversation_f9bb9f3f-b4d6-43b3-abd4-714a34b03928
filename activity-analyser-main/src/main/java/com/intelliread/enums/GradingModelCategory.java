package com.intelliread.enums;

import lombok.Getter;

/**
 * Enum representing the category type for grading models.
 * <p>
 * This enum defines two types of grading model categories:<br>
 * - DIAGNOSTIC: Represents diagnostic test grading models.<br>
 * - COURSE: Represents course grading models.<br>
 * <p>
 * Each category type has an associated code and name.
 *
 * <AUTHOR>
 * @since 1.0.1
 */
@Getter
public enum GradingModelCategory {

    DIAGNOSTIC("DIAGNOSTIC", "diagnostic"), COURSE("COURSE", "course");

    final String code;
    final String name;

    GradingModelCategory(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
