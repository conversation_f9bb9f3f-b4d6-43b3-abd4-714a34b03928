package com.intelliread.enums;

public enum ChangeAndSharePersona {
	
	ADMINISTRATION("Administration", "ADMINISTRATION"),
	ACADEMIC_STAFF("Academic Staff", "ACADEMIC_STAFF"),
	ADMIN_USERS("Admin Users", "ADMIN_USERS"),
	STUDENT("Student", "STUDENT");
	
	private String name;
	
	private String code;
	
	ChangeAndSharePersona(String name, String code){
		this.name = name;
		this.code = code;
	}
	
	public String getName() {
		return name;
	}
	
	public String getCode() {
		return code;
	}
}
