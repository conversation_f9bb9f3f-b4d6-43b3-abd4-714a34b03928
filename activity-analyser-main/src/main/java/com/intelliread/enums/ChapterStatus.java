package com.intelliread.enums;

/**
 * Content Reviewer has to approve or reject the chapter
 * 
 * <AUTHOR>
 * @since 1.1.0
 *
 */
public enum ChapterStatus {

	PENDING("PENDING", "Pending"), APPROVED("APPROVED", "Approved"), REJECTED("REJECTED", "Rejected");

	String code;

	String name;

	ChapterStatus(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

}
