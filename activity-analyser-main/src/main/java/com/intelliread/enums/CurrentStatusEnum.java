package com.intelliread.enums;

public enum CurrentStatusEnum {
	COMPLETED("COMPLETED", "Completed"), ASSIGNED("ASSIGNED", "Assigned"), IN_PROGRESS("IN_PROGRESS", "In_Progress"),
	NOMINATED("NOMINATED", "Nominated"),VOLUNTEERED("VOLUNTEERED", "Volunteered");

	String code;

	String name;

	CurrentStatusEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}


}
