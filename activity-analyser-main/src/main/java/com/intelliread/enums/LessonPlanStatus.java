package com.intelliread.enums;

public enum LessonPlanStatus {
	ACTIVE("ACTIVE", "Active"), INACTIVE("INACTIVE", "Inactive"), ARCHIVED("ARCHIVED", "Archived"),
	DRAFT("DRAFT", "Draft"), ON_HOLD("ON_HOLD", "OnHold");

	String code;

	String name;

	LessonPlanStatus(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}
}
