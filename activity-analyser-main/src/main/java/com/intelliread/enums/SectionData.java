package com.intelliread.enums;

/**
 * <AUTHOR>
 * @since 1.0.2
 *
 */
public enum SectionData {

	NO_SECTION("I don't have the section data", "NO_SECTION"),
	SAME_SECTION("All grades have same section", "SAME_SECTION"),
	DIFFERENT_SECTIONS("Grades have different sections", "DIFFERENT_SECTIONS");

	private String name;

	private String code;

	SectionData(String name, String code) {
		this.name = name;
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}
}
