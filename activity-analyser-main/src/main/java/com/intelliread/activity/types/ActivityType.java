package com.intelliread.activity.types;

public enum ActivityType {
    CHAPTER_COMPLETE,
    SPEAK_ALOUD,
    INCORRECT_SPEAK_ALOUD,
    ASSESSMENT,
    LISTEN;

    public static boolean isValidType(String type) {
        for (ActivityType activityType : values()) {
            if (activityType.name().equalsIgnoreCase(type)) {
                return true;
            }
        }
        return false;
    }
}
