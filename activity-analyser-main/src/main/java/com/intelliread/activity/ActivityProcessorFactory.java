package com.intelliread.activity;

import com.intelliread.activity.processors.*;
import com.intelliread.activity.types.ActivityType;
import com.intelliread.repository.master.ActivityProcessor;

import java.util.HashMap;
import java.util.Map;

public class ActivityProcessorFactory {
    private static final Map<ActivityType, ActivityProcessor> processors = new HashMap<>();

    static {
        processors.put(ActivityType.CHAPTER_COMPLETE, new ChapterCompleteProcessor());
        processors.put(ActivityType.SPEAK_ALOUD, new SpeakAloudProcessor());
        processors.put(ActivityType.INCORRECT_SPEAK_ALOUD, new IncorrectSpeechProcessor());
        processors.put(ActivityType.ASSESSMENT, new AssessmentProcessor());
        processors.put(ActivityType.LISTEN, new ListenProcessor());
    }

    public static ActivityProcessor getProcessor(ActivityType type) {
        return processors.get(type);
    }
}
