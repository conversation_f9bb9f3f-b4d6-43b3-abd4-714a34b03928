package com.intelliread.activity.processors;

import com.intelliread.constants.ActivityConstants;
import com.intelliread.model.Activity;
import com.intelliread.repository.master.ActivityProcessor;

public class IncorrectSpeechProcessor implements ActivityProcessor {
    @Override
    public int process(Activity activity) {
        ActivityConstants incorrectSpeechAloud = ActivityConstants.INCORRECT_SPEAK_ALOUD;

        return incorrectSpeechAloud.getRewardThreshold();
    }
}
