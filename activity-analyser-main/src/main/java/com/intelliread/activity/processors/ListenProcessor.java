package com.intelliread.activity.processors;

import com.intelliread.constants.ActivityConstants;
import com.intelliread.model.Activity;
import com.intelliread.repository.master.ActivityProcessor;

public class ListenProcessor implements ActivityProcessor {
    @Override
    public int process(Activity activity) {
        ActivityConstants listen = ActivityConstants.ASSESSMENT;

        return listen.getRewardThreshold();
    }
}
