package com.intelliread.activity.processors;

import com.intelliread.constants.ActivityConstants;
import com.intelliread.model.Activity;
import com.intelliread.entity.master.SpeakAloud;
import com.intelliread.repository.master.ActivityProcessor;

public class SpeakAloudProcessor implements ActivityProcessor {
    @Override
    public int process(Activity activity) {
        ActivityConstants speakAloud = ActivityConstants.ASSESSMENT;
        int totalScore = speakAloud.getRewardThreshold();

        if (activity.getSpeakAloud() != null) {
            for (SpeakAloud detail : activity.getSpeakAloud()) {
                totalScore += detail.getReadingScore();
            }
        }

        return totalScore;
    }
}
