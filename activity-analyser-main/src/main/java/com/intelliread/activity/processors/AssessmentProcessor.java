package com.intelliread.activity.processors;

import com.intelliread.constants.ActivityConstants;
import com.intelliread.model.Activity;
import com.intelliread.repository.master.ActivityProcessor;

import java.time.Duration;
import java.time.Instant;

public class AssessmentProcessor implements ActivityProcessor {
    @Override
    public int process(Activity activity) {
        Instant startTime = Instant.parse(activity.getStartTime());
        Instant endTime = Instant.parse(activity.getEndTime());
        ActivityConstants assessment = ActivityConstants.ASSESSMENT;

        long durationInSeconds = Duration.between(startTime, endTime).getSeconds();
        int extraScore = activity.getAssessmentScore();

        if (durationInSeconds < assessment.getThresholdTime()) {
            extraScore = (extraScore + assessment.getRewardThreshold());
        }

        return extraScore;
    }
}
