package com.intelliread.activity.processors;

import com.intelliread.constants.ActivityConstants;
import com.intelliread.model.Activity;
import com.intelliread.repository.master.ActivityProcessor;

import java.time.Instant;

public class ChapterCompleteProcessor implements ActivityProcessor {
    @Override
    public int process(Activity activity) {
        ActivityConstants chapterComplete = ActivityConstants.CHAPTER_COMPLETE;
        long timeTaken = Instant.parse(activity.getEndTime()).toEpochMilli() - Instant.parse(activity.getStartTime()).toEpochMilli();
        long thresholdTime = chapterComplete.getThresholdTime();
        return timeTaken < thresholdTime ? chapterComplete.getRewardThreshold() : chapterComplete.getDefaultReward();
    }
}
