package com.intelliread.constants;

import com.intelliread.entity.master.UserActivity;

public enum ActivityConstants {

    // Define activity types with properties
    CHAPTER_COMPLETE("CHAPTER_COMPLETE", 10, 0, 5),
    SPEAK_ALOUD("SPEAK_ALOUD", 2, 0, 0),
    INCORRECT_SPEAK_ALOUD("INCORRECT_SPEAK_ALOUD", -2, 0, 0),
    ASSESSMENT("ASSESSMENT", 2, 60, 0),
    LISTEN("LISTEN", 1, 0, 0);

    private final String type;          // Primary Key
    private final int rewardThreshold;  // Reward or penalty score
    private final int thresholdTime;    // Threshold time in seconds
    private final int defaultReward;    // In case there is no reward for a task

    // Constructor
    ActivityConstants(String type, int rewardThreshold, int thresholdTime, int defaultReward) {
        this.type = type;
        this.rewardThreshold = rewardThreshold;
        this.thresholdTime = thresholdTime;
        this.defaultReward = defaultReward;
    }

    // Getters
    public String getType() {
        return type;
    }

    public int getRewardThreshold() {
        return rewardThreshold;
    }

    public int getThresholdTime() {
        return thresholdTime;
    }

    public int getDefaultReward() {
        return defaultReward;
    }

    public UserActivity.ActivityScoreConfig toActivityScoreConfig() {
        UserActivity.ActivityScoreConfig config = new UserActivity.ActivityScoreConfig();
        config.setType(this.type);
        config.setRewardThreshold(this.rewardThreshold);
        config.setThresholdTime(this.thresholdTime);
        config.setDefaultReward(this.defaultReward);
        return config;
    }

    // Optional: Find an activity by type
    public static ActivityConstants fromType(String type) {
        for (ActivityConstants activity : values()) {
            if (activity.type.equals(type)) {
                return activity;
            }
        }
        throw new IllegalArgumentException("Invalid activity type: " + type);
    }

    public static boolean isValidType(String type) {
        for (ActivityConstants activity : values()) {
            if (activity.type.equals(type)) {
                return true;
            }
        }
        return false;
    }
}
