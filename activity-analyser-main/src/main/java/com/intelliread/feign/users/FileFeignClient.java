package com.intelliread.feign.users;

import com.intelliread.config.FiegnConfiguration;
import com.intelliread.response.models.LMSResponse;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;



@FeignClient(name = "FILE-UPLOAD", configuration = FiegnConfiguration.class, url="${api.fileupload.url}")
public interface FileFeignClient {

	@PostMapping(value = "/v1/api/file/upload",consumes = "multipart/form-data")
	@Headers("Content-Type: multipart/form-data")
	public LMSResponse<String> upload(@RequestParam(name = "fileCategory") FileCategories category,
			@RequestParam(name = "subFolders") SubFolders subFolders, @RequestPart(name = "file") MultipartFile file);
	
}