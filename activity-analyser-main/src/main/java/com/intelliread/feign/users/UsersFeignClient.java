package com.intelliread.feign.users;

import com.intelliread.config.FiegnConfiguration;
import com.intelliread.response.dto.BranchesMinResponse;
import com.intelliread.response.dto.NameCommonResponseDto;
import com.intelliread.response.dto.TeacherAssignmentResponse;
import com.intelliread.response.dto.UserMinResponseDto;
import com.intelliread.response.models.LMSResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Call the API's from the service USERS-SERVICE, This interface only designed
 * for the USERS-SERVICE.
 * 
 * <AUTHOR>
 * @since 1.0.0
 *
 */
@FeignClient(name = "USER-SERVICE", configuration = FiegnConfiguration.class, url="${api.user.service.url}")
public interface UsersFeignClient {

	@GetMapping("/v1/api/user/users/username/{username}")
	public LMSResponse<UsersFeignDto> getUsersByUsernameForFeign(@PathVariable("username") String username);

	// branches
	@GetMapping("/v1/api/user/branches/{planId}/mappings")
	public LMSResponse<List<BranchesMinResponse>> getAllBranchMappingsForPlan(@PathVariable("planId") String planId);

	@DeleteMapping("/v1/api/user/branches/{planId}/mappings")
	public LMSResponse<Boolean> deleteAllBranchMappingsForPlan(@PathVariable("planId") String planId);

	@GetMapping("/v1/api/user/branches/{planId}/active/mappings")
	public LMSResponse<Boolean> updateActiveFieldOfAllBranchMappingsForPlan(@PathVariable("planId") String planId,
			@RequestParam(value = "active") boolean active);

	@GetMapping("/v1/api/user/branches/{planId}/plan-mappings")
	public LMSResponse<Boolean> checkPlanHasAnyMapping(@PathVariable("planId") String planId);

	// boards (schools and branches)
	@GetMapping("/v1/api/user/schools/{boardId}/board-mappings")
	public LMSResponse<Boolean> getSchoolsAndBranchesMappedDataForBoard(@PathVariable("boardId") String boardId);

	// cities (schools and branches)
	@GetMapping("/v1/api/user/schools/{cityId}/city-mappings")
	public LMSResponse<Boolean> checkCityMappedData(@PathVariable("cityId") String cityId);

	// grade-section mapping
	@GetMapping("/v1/api/user/grade-section/{gradeId}/grade-mappings")
	public LMSResponse<Boolean> getGradeSectionMappedDataForGrade(@PathVariable("gradeId") String gradeId);

	@GetMapping("/v1/api/user/grade-section/{sectionId}/section-mappings")
	public LMSResponse<Boolean> getGradeSectionMappedDataForSection(@PathVariable("sectionId") String sectionId);

	@GetMapping("/v1/api/user/grade-section/checking-mapping-for-grade/{gradeId}")
	public LMSResponse<Boolean> checkTheGradeHasMapping(@PathVariable("gradeId") String gradeId);

	// students
	@GetMapping("/v1/api/user/students/{gradeId}/grade-mappings")
	public LMSResponse<Boolean> getStudentMappedDataForGrade(@PathVariable("gradeId") String gradeId);

	@GetMapping("/v1/api/user/students/{languageId}/language-mappings")
	public LMSResponse<List<NameCommonResponseDto>> getAllStudentsByLanguage(
			@PathVariable("languageId") String languageId);

	@GetMapping("/v1/api/user/students/{studentCategoryId}/student-category-mappings")
	public LMSResponse<List<NameCommonResponseDto>> getStudentsByStudentCategory(
			@PathVariable("studentCategoryId") String studentCategoryId);

	// sections
	@GetMapping("/v1/api/user/students/{sectionId}/section-mappings")
	public LMSResponse<Boolean> getMappedDataForSections(@PathVariable("sectionId") String sectionId);

	// teacher
	@GetMapping("/v1/api/user/teachers/coordinatorType-mapping/{coordinatorTypeId}")
	public LMSResponse<List<NameCommonResponseDto>> getAllTeacherMappingsForCoordinatorType(
			@PathVariable("coordinatorTypeId") String coordinatorTypeId);

	@GetMapping("/v1/api/user/teachers/checking/coordinator-mapping/{coordinatorTypeId}")
	public LMSResponse<Boolean> checkTheMappingForCoordinatoryType(
			@PathVariable("coordinatorTypeId") String coordinatorTypeId);

	@GetMapping("/v1/api/user/teachers/checking/subject-mapping/{subjectId}")
	public LMSResponse<Boolean> checkTheMappingForSubject(@PathVariable("subjectId") String subjectId);

	@GetMapping("/v1/api/user/teachers/checking/sub-topic-mapping/{subTopicId}")
	public LMSResponse<Boolean> checkTheMappingForSubTopic(@PathVariable("subTopicId") String subTopicId);

	// users
	@GetMapping("/v1/api/user/users/role-mapping/{roleId}")
	public LMSResponse<List<UserMinResponseDto>> getAllUserResponsesForRole(@PathVariable("roleId") String roleId);

	@GetMapping("/v1/api/user/users/count-role-id")
	public LMSResponse<Long> countRoleIdForDeletion(@RequestParam("roleId") String roleId);

	@GetMapping("/v1/api/user/users/user-details/by-role")
	public LMSResponse<List<UsersRoleResponseDto>> getAllAdminUsersRole(
			@RequestParam(value = "roleId", required = false) List<String> roleIds);

	// misc
	@GetMapping("/v1/api/user/misc/checking/grade_section_mapping/{academicYearId}")
	public LMSResponse<Boolean> checkTheGradeSectionMappingForAcademicYearId(
			@PathVariable("academicYearId") String academicYearId);

	// Admin Users
	@PutMapping("/v1/api/user/admin-users/toggle-active-status")
	public LMSResponse<Boolean> toggleActiveStatusForMaster(@RequestParam("id") String id,
			@RequestParam("active") boolean active);
	
	@GetMapping("/v1/api/user/branches/feign-school/details")
	public LMSResponse<List<SchoolCountDetailsResponseDto>> getSchoolsDetails(
			@RequestParam(value = "boardIds", required = true) List<String> boardIds);
	
	@GetMapping("/v1/api/user/schools/cities-school/details")
	public LMSResponse<List<CitiesSchoolCountDetailsResponseDto>> getCitiesSchoolDetails(
			@RequestParam(value = "cityIds", required = true) List<String> cityIds);
	
	
	// grade-section mapping
	@GetMapping("/v1/api/user/grade-section/getMappedDataForGradeId/{gradeId}")
	public LMSResponse<List<String>> getMappedDataForGradeId(@PathVariable("gradeId") String gradeId);
	
	// getTeachersAndAssignedSections
	@GetMapping("/v1/api/user/teachers/getTeachersAndAssignedSections")
	public LMSResponse<List<TeacherAssignmentResponse>> getTeachersAndAssignedSections(@RequestParam("gradeId") String gradeId,
			@RequestParam("subjectId") String subjectId);
	
	@PostMapping("/v1/api/user/students/ir-status-update")	
	public LMSResponse<Boolean> updateStudentEntityIrStatus();
	
	@PutMapping("/v1/api/user/students/ir-status-update")	
	public LMSResponse<Boolean> updateStudentEntityIrStatus(@RequestParam("studentId") String studentId);

}
