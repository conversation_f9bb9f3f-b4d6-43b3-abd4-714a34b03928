package com.intelliread.feign.master;

import com.intelliread.response.users.dto.SubTopicBulkInnerResponseDto;
import lombok.Data;

import java.util.List;

@Data
public class SubjectsResponseDto {

	private String id;

	private String subject;

	private Integer chapterCount;

	private Integer quizCount;

	private Integer score;

	private String subjectTypeId;

	private String subjectType;

	private String discription;

	private boolean active;

	private List<SubTopicBulkInnerResponseDto> subTopics;
}
