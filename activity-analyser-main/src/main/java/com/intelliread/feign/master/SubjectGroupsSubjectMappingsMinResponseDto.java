package com.intelliread.feign.master;

import com.intelliread.response.dto.SubTopicCountersResponseDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubjectGroupsSubjectMappingsMinResponseDto {
//    private String id;

    private String subjectId;
    private String subject;
    private List<SubTopicCountersResponseDto> subTopics;
    private Integer chapterCount;
    private Integer quizCount;
    private Integer score;
    private String lastUpadatedAt;
}
