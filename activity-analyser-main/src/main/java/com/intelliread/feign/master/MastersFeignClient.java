package com.intelliread.feign.master;

import com.intelliread.config.FiegnConfiguration;
import com.intelliread.model.GradesSubjectModel;
import com.intelliread.model.SubjectAndSubTopicIds;
import com.intelliread.request.dto.GradeSectionResponseDto;
import com.intelliread.request.dto.PlanFinderRequestDto;
import com.intelliread.response.boardgradesubjectmapping.dto.BoardsGradesSubjectsSubTopicsJsonDto;
import com.intelliread.response.contentdata.dto.SubjectContentResponseDto;
import com.intelliread.response.dto.*;
import com.intelliread.response.gradecontent.GradeContentResponseDto;
import com.intelliread.response.models.GradesAndPlans;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.SubjectsSubtopicUnderGradeModel;
import com.intelliread.utilities.Constants;
import jakarta.validation.constraints.NotBlank;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Call the API's from the service MASTER-SERVICE, This interface only designed
 * for the MASTER-SERVICE.
 * 
 * <AUTHOR> C Achari
 * <AUTHOR> Mandal
 * @since 1.0.0
 *
 */

@FeignClient(name = "MASTER-SERVICE", configuration = FiegnConfiguration.class, url="${api.master.service.url}")
public interface MastersFeignClient {

	// roles
	@GetMapping("/v1/api/master/roles/{id}")
	public LMSResponse<RolesFeignDto> getRolesById(@PathVariable("id") String id);

	@GetMapping("/v1/api/master/roles/all/{ids}")
	public LMSResponse<List<RolesFeignDto>> getRolesByIds(@PathVariable("ids") List<String> ids);

	@GetMapping("/v1/api/master/role-menu-map/by/{roles}")
	public LMSResponse<List<MenuSubMenuResponseDto>> getAllRolesMenuMappingByRoles(
			@PathVariable("roles") List<String> roles);

	// grades
	@GetMapping("/v1/api/master/grades/all-by-ids")
	public LMSResponse<List<GradesResponseDto>> getAllGradesByIds(@RequestParam("ids") List<String> ids);

	@GetMapping("/v1/api/master/grades/is-exist")
	public LMSResponse<Boolean> existGradesByIds(@RequestParam("ids") List<String> ids);

	@GetMapping("/v1/api/master/grades/{id}")
	public LMSResponse<GradesResponseDto> getGradesById(@PathVariable("id") String id);

	@GetMapping("/v1/api/master/grades/by-name/{name}")
	public LMSResponse<String> getGradeIdByName(@PathVariable("name") String name);

	@PutMapping("/v1/api/master/grades/assigned-grades-sections")
	public LMSResponse<List<GradesSectionFeignResponseDto>> assignedGradesAndSectionForAcademicStaffs(
			@RequestBody List<GradeSectionResponseDto> request);
	
	
	// sections
	@GetMapping("/v1/api/master/sections/all-by-ids")
	public LMSResponse<List<SectionsResponseDto>> getAllSectionsByIds(@RequestParam("ids") List<String> ids);

	@GetMapping("/v1/api/master/sections/is-exist")
	public LMSResponse<Boolean> existSectionsByIds(@RequestParam("ids") List<String> ids);

	@GetMapping("/v1/api/master/sections/{id}")
	public LMSResponse<SectionsResponseDto> getSectionsById(@RequestParam("id") String id);

	@GetMapping("/v1/api/master/sections/all")
	public LMSResponse<List<SectionsResponseDto>> getAllSections(@RequestParam("search") String search);

	// academic year
	@GetMapping("/v1/api/master/academic-years/all-by-ids")
	public LMSResponse<List<AcademicYearResponseDto>> getAllAcademicYearsByIds(@RequestParam("ids") List<String> ids);

	@GetMapping("/v1/api/master/academic-years/is-exist")
	public LMSResponse<Boolean> existAcademicYearsByIds(@RequestParam("ids") List<String> ids);

	@GetMapping("/v1/api/master/academic-years/{id}")
	public LMSResponse<AcademicYearResponseDto> getAcademicYearsById(@PathVariable("id") String id);

	@GetMapping("/v1/api/master/academic-years/latest")
	public LMSResponse<AcademicYearResponseDto> getLatestAcademicYear();

	// Languages
	@GetMapping("/v1/api/master/languages/all")
	public LMSResponse<List<LanguagesResponseDto>> getAllLanguages();

	@GetMapping("/v1/api/master/languages/{id}")
	public LMSResponse<LanguagesResponseDto> getLanguagesById(@PathVariable("id") String id);

	@GetMapping("/v1/api/master/languages/all-by-ids")
	public LMSResponse<List<LanguagesResponseDto>> getLanguagesByIds(@RequestParam("ids") List<String> ids);

	// boards
	@GetMapping("/v1/api/master/boards/{id}")
	public LMSResponse<BoardsResponseDto> getBoardsById(@PathVariable("id") String id);

	@GetMapping("/v1/api/master/boards/by-ids")
	public LMSResponse<List<BoardsResponseDto>> getAllBoardById(@RequestParam("id") List<String> id);

	// countries
	@GetMapping("/v1/api/master/countries/all")
	public LMSResponse<List<CountriesResponseDto>> getCountries(
			@RequestParam(value = "search", required = false) String search);

	@GetMapping("/v1/api/master/countries/{id}")
	public LMSResponse<CountriesResponseDto> getCountriesById(@PathVariable("id") String id);

	// districts
	@GetMapping("/v1/api/master/districts/all")
	public LMSResponse<List<DistrictsResponseDto>> getAllDistricts(
			@RequestParam(value = "search", required = false) String search);

	@GetMapping("/v1/api/master/districts/{id}")
	public LMSResponse<DistrictsResponseDto> getDistrictsById(@PathVariable("id") String id);

	// pincodes
	@GetMapping("/v1/api/master/pincodes/all")
	public LMSResponse<List<PincodesResponseDto>> getAllPincodes(
			@RequestParam(value = "stateId", required = false) String stateId,
			@RequestParam(value = "districtId", required = false) String districtId,
			@RequestParam(value = "search", required = false) String search);

	@GetMapping("/v1/api/master/pincodes/{id}")
	public LMSResponse<PincodesResponseDto> getPincodesById(@PathVariable("id") String id);

	// plans
	@GetMapping("/v1/api/master/plans/{id}")
	public LMSResponse<PlansResponseDto> getPlansById(@PathVariable("id") String id);

	@GetMapping("/v1/api/master/plans/subjects-from-plan-template")
	public LMSResponse<List<SubjectsResponseDto>> subjectListFromPlanTemplate(
			@RequestParam(value = "gradeId") String gradeId, @RequestParam(value = "planId") String planId);

	@GetMapping("/v1/api/master/plans/grades-from-plan/{id}")
	public LMSResponse<List<String>> assignedGradesFromPlan(@PathVariable("id") String id);

	@GetMapping("/v1/api/master/plans/purchased-features")
	public LMSResponse<List<PlanTemplateImplResponseDto>> purchasedPlanTemplate(
			@RequestParam(value = "planId", required = true) String planId,
			@RequestParam(value = "gradeIds", required = false) List<String> gradeIds,
			@RequestParam(value = "roleName", required = true) String roleName,
			@RequestParam(value = "subjectIds", required = false) List<String> subjectIds);

	@GetMapping("/v1/api/master/plans/subjects-by/purchased-feature")
	public LMSResponse<List<String>> listOfSubjectByPuchasedPlanFeature(
			@RequestParam(value = "planId", required = true) String planId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "columnName", required = true) String columnName);

	@GetMapping("/v1/api/master/plans/get-by-ids")
	public LMSResponse<List<PlansResponseDto>> getAllPlansByIds(@RequestParam("ids") List<String> ids);

	@PutMapping("/v1/api/master/plans/similar")
	public LMSResponse<List<String>> similarPlans(@RequestBody PlanFinderRequestDto request);

	@GetMapping("/v1/api/master/plans/subject-subtopic/listing")
	public LMSResponse<List<SubjectsSubtopicUnderGradeModel>> getTheSubjectsAndSimilarPlans(
			@RequestParam(value = "gradeIds", required = false) List<String> gradeIds,
			@RequestParam(value = "planId", required = true) String planId);

	@GetMapping("/v1/api/master/plans/simila/grade-wise")
	public LMSResponse<GradesAndPlans> getSimilarPlansGradeWise(
			@RequestParam(value = "planId", required = true) String planId,
			@RequestParam(value = "coordinatorId", required = false) String coordinatorId);

	@GetMapping("/v1/api/master/plans/csv/for-student")
	public LMSResponse<Map<String, List<String>>> getAllPlanAndItsGrades();

	// states
	@GetMapping("/v1/api/master/states")
	public LMSResponse<List<StatesResponseDto>> getAllStates(
			@RequestParam(value = "countryId", required = false) String countryId,
			@RequestParam(value = "search", required = false) String search);

	@GetMapping("/v1/api/master/states/{id}")
	public LMSResponse<StatesResponseDto> getStatesById(@PathVariable("id") String id);

	// student categories
	@GetMapping("/v1/api/master/student-categories/all")
	public LMSResponse<List<StudentCategoriesResponseDto>> getAllStudentCategories();

	@GetMapping("/v1/api/master/student-categories/{id}")
	public LMSResponse<StudentCategoriesResponseDto> getStudentCategoryById(@PathVariable("id") String id);

	// subjects
	@GetMapping("/v1/api/master/subjects/all")
	public LMSResponse<List<SubjectsResponseDto>> getAllSubjects();

	@GetMapping("/v1/api/master/subjects/{id}")
	public LMSResponse<SubjectsResponseDto> getSubjectsById(@PathVariable("id") String id);

	@GetMapping("/v1/api/master/subjects/all/{ids}")
	public LMSResponse<List<SubjectsMinResponseDto>> getAllSubjectsByIds(@PathVariable("ids") List<String> ids);

	@PutMapping("/v1/api/master/subjects/min-subject-details")
	public LMSResponse<List<SubjectsMinResponseDto>> getAllSubjectsBySelectedIds(
			@RequestBody List<SubjectAndSubTopicIds> request);

	@GetMapping("/v1/api/master/subjects/min-subject-details/principal-coordinator")
	public LMSResponse<List<SubjectsMinResponseDto>> getAllSubjectsByPlanIdGradeIdOrCoordinatorId(
			@RequestParam(value = "planId", required = true) String planId,
			@RequestParam(value = "coordinatorId", required = false) String coordinatorId,
			@RequestParam(value = "gradeId", required = false) String gradeId);

	// subject group subject mappings
	@GetMapping("/v1/api/master/subject-groups/mapping/{mappingId}")
	public LMSResponse<SubjectGroupsSubjectMappingsResponseDto> getSubjectGroupsSubjectMappingsById(
			@PathVariable("mappingId") String mappingId);

	// subject group subject mappings
	@GetMapping("/v1/api/master/subject-groups/mapping/by-ids")

	public LMSResponse<List<SubjectGroupsSubjectMappingsResponseDto>> getAllSubjectGroupsSubjectMappingsByIds(
			@RequestParam("mappingIds") List<String> mappingIds);

	// subject group subject mappings with sub-topic list
	@GetMapping("/v1/api/master/subject-groups/mapping-and-subtopics/by-ids")
	public LMSResponse<List<SubjectGroupsSubjectMappingsMinResponseDto>> getAllSubjectGroupsSubjectMappingsAndSubTopicsByIds(
			@RequestParam("mappingIds") List<String> mappingIds);

	// subject types
	@GetMapping("/v1/api/master/subject-types/all")
	public LMSResponse<List<SubjectTypesResponseDto>> getAllSubjectTypesById();

	@GetMapping("/v1/api/master/subject-types/{id}")
	public LMSResponse<SubjectTypesResponseDto> getSubjectTypesById(@PathVariable("id") String id);

	// role
	@GetMapping("/v1/api/master/roles/by-key")
	public LMSResponse<String> getOnlyIdByRole(@RequestParam("key") String key);

	// cities
	@GetMapping("/v1/api/master/cities/by-ids")
	public LMSResponse<List<CitiesResponseDto>> getAllCitiesById(@RequestParam("id") List<String> id);

	@GetMapping("/v1/api/master/cities/feign/{id}")
	public LMSResponse<CitiesResponseDto> getCityById(@PathVariable("id") String id);

	// Coordinator type
	@GetMapping("/v1/api/master/coordinator-types/feign/{id}")
	public LMSResponse<CoordinatorTypeResponseDto> feignCoordinatorTypeById(@PathVariable("id") String id);

	@GetMapping("/v1/api/master/coordinator-types/feign")
	public LMSResponse<List<CoordinatorTypeResponseDto>> feignAllCoordinatorTypeByIds(
			@RequestParam("ids") List<String> ids);

	@GetMapping("/v1/api/master/coordinator-types/feign/all-by-ids")
	public LMSResponse<Boolean> feignCheckAllCoordinatorTypeByIds(@RequestParam("ids") List<String> ids);

	@GetMapping("/v1/api/master/coordinator-types/assigned-grades")
	public LMSResponse<List<String>> getGradeListByCoordinatorId(@RequestParam(value = "id") String id);

	@GetMapping("/v1/api/master/coordinator-types/feign/assigned-grades")
	public LMSResponse<List<String>> getGradesByCoordinatorId(@RequestParam(value = "id") String id);

	// Configuration table
	@GetMapping("/v1/api/master/configurations/reset-password-ui")
	public LMSResponse<String> getResetPasswordWithBaseUrl(
			@RequestParam("lmsEnv") @NotBlank(message = Constants.MANDATORY_FIELD) String lmsEnv); // no need of JWT
																									// token

	@GetMapping("/v1/api/master/configurations/change-password-ui")
	public LMSResponse<String> changePasswordWithBaseUrl(
			@RequestParam("lmsEnv") @NotBlank(message = Constants.MANDATORY_FIELD) String lmsEnv);

	@GetMapping("/v1/api/master/configurations/by-key")
	public LMSResponse<String> getValueByKey(
			@RequestParam("key") @NotBlank(message = Constants.MANDATORY_FIELD) String key);

	// subtopic
	@GetMapping("/v1/api/master/sub-topics/all/{ids}")
	public LMSResponse<List<SubTopicsMinResponseDto>> getAllSubTopicsByIds(@PathVariable("ids") List<String> ids);

	// counters
	@GetMapping("/v1/api/master/counters/branch")
	public LMSResponse<List<Long>> branchIds(@RequestParam(value = "branchId", required = true) String branchId);

	@GetMapping("/v1/api/master/counters/student")
	public LMSResponse<List<Long>> studentIds(@RequestParam(value = "studentId", required = true) String studentId);

	@GetMapping("/v1/api/master/sub-topics/feign/{id}")
	public LMSResponse<SubTopicsMinResponseDto> getBySubTopicId(@PathVariable("id") String id);

	@PostMapping("/v1/api/master/chapters/get/chapterIds")
	public LMSResponse<List<String>> getAllChapterIdsByGradesSubjects(@RequestBody List<GradesSubjectModel> request);

	@GetMapping("/v1/api/master/chapters/count")
	public LMSResponse<Integer> chapterCount(@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId);

	@GetMapping("/v1/api/master/chapters/count-coordinator")
	public LMSResponse<Integer> chapterCountForCoordinator(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId);

	@GetMapping("/v1/api/master/chapters/count-principal")
	public LMSResponse<Integer> chapterCountForPrincipal(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId);

	@GetMapping("/v1/api/master/chapters/count-teacher")
	public LMSResponse<Long> getchapterCountForTeacher(@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId);

	// Misc
	@GetMapping("/v1/api/master/misc/all-type-csv")
	public LMSResponse<CsvFileResponseDto> getALLCsvTypeOfIdsByNames(
			@RequestParam(value = "languages", required = false) Set<String> languages,
			@RequestParam(value = "grades", required = false) List<String> grades,
			@RequestParam(value = "sections", required = false) List<String> sections,
			@RequestParam(value = "studentCategories", required = false) List<String> studentCategories);

	// Chapters
	@GetMapping("/v1/api/master/chapters/feign/{id}")
	public LMSResponse<ChapterFeignResponseDto> getChapterByIdForFeignCall(@PathVariable("id") String id);

	@GetMapping("/v1/api/master/chapters/feign-all")
	public LMSResponse<List<ChapterFeignResponseDto>> getAllChaptersByIdForFeign(@RequestParam("id") List<String> id);

	@GetMapping("/v1/api/master/boards/board-details")
	public LMSResponse<BoardsResponseDto> findBoardsById(@RequestParam("boardId") String boardId);

	@GetMapping("/v1/api/master/cities/feign-city-details")
	public LMSResponse<CitiesResponseDto> findCitiesById(@RequestParam("cityId") String cityId);

	@GetMapping("/v1/api/master/plans/feign-plan-details")
	public LMSResponse<PlansResponseDto> findPlansById(@RequestParam("planId") String planId);

	@GetMapping("/v1/api/master/academic-years/latest-academic/year-id")
	public LMSResponse<String> getLatestAcademicYearId();

	@GetMapping("/v1/api/master/plans/for-other-admins")
	public LMSResponse<com.intelliread.response.dto.PlansResponseDto> getPlanDetails(@RequestParam("id") String planId);

	@GetMapping("/v1/api/master/subjects/sub-topics/feign")
	public LMSResponse<List<SubjectsSubTopicsJsonDto>> getSubjectsSubTopicsDetails();

	@GetMapping("/v1/api/master/boards/grades/subjects/subTopics/feign")
	public LMSResponse<List<BoardsGradesSubjectsSubTopicsJsonDto>> getGradesSubjectsSubTopicsDetails();

	@GetMapping("/v1/api/master/cities/entity/feign")
	public LMSResponse<List<CitiesEntityResponseDto>> getCitiesEntityDetailsASCOrder();

	@GetMapping("/v1/api/master/boards/grades/subjects/content/feign")
	public LMSResponse<List<com.intelliread.response.chapters.dto.BoardsGradesSubjectsChaptersJsonDto>> getGradesSubjectsChaptersDetails();

	@GetMapping("/v1/api/master/subjects/sub-topics/chapter/feign")
	public LMSResponse<List<SubjectContentResponseDto>> getAllSubjectsSubtopicsChapterDetails();

	@GetMapping("/v1/api/master/grades/section/subjects/sub-topics/chapter/feigncall")
	public LMSResponse<List<GradeContentResponseDto>> getAllGradeSubjectsChapterDetailsList();
}