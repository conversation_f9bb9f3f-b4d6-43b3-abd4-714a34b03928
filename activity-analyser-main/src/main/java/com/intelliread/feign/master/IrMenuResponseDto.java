package com.intelliread.feign.master;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class IrMenuResponseDto {
	
//	@ApiModelProperty(value = "Menu Id", example = "ff80818180cce30c0180cd26560d0000", position = 1)
//	private String menuId;
//	
//	@ApiModelProperty(value = "Menu name", example = "Master", position = 2)
//	private String menu;
//	
//	@ApiModelProperty(value = "application", position = 3)
//	private String application;
//	
//	@ApiModelProperty(value = "iconClass", position = 4)
//	private String iconClass;
//	
//	@ApiModelProperty(value = "routeu", position = 5)
//	private String route;
//	
//	@ApiModelProperty(value = "Sub-menu associate with a Menu", position = 6)
//	private List<SubMenuResponseDto> subMenus;
	
	private int order;
	
	private String key;
	
	private String title;
	
	private String route;
	
	private String icon;
	
	private String roles;
	
//	@ApiModelProperty(value = "Sub-menu associate with a Menu", position = 6)
//	private List<SubMenuResponseDto> subMenus;
	
	
}