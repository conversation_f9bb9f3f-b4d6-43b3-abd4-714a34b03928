package com.intelliread.feign.notification;

import com.intelliread.config.FiegnConfiguration;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.request.dto.CreateUserEmailRequestDto;
import com.intelliread.request.dto.EmailRequestDto;
import com.intelliread.request.dto.SmsAlertBody;
import com.intelliread.response.dto.EmailResponseDto;
import com.intelliread.response.dto.ShareDetailsResponseDto;
import com.intelliread.response.dto.SmsGatewayRequestDto;
import com.intelliread.response.dto.SmsSendResponseDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;

@FeignClient(name = "NOTIFICATION-SERVICE", configuration = FiegnConfiguration.class, url="${api.notification.url}")
public interface NotificationFeignClient {

	// EMAIL
	@PostMapping("/v1/api/notification/emails/createUser")
	public LMSResponse<EmailResponseDto> userCreated(@RequestBody CreateUserEmailRequestDto request);

	@PostMapping("/v1/api/notification/emails/edit-user")
	public LMSResponse<EmailResponseDto> editUser(@RequestBody CreateUserEmailRequestDto request);

	@PostMapping("/v1/api/notification/emails/forgot-password")
	public LMSResponse<EmailResponseDto> forgotPassword(@Valid @RequestBody EmailRequestDto request);

	@PostMapping("/v1/api/notification/emails/share-details")
	public LMSResponse<EmailResponseDto> shareDetails(@RequestBody ShareDetailsResponseDto request);

	@PostMapping("/v1/api/notification/emails/update-password")
	public LMSResponse<EmailResponseDto> updatePassword(@RequestBody CreateUserEmailRequestDto request);

	@PostMapping("/v1/api/notification/emails/school-created-or-update")
	public LMSResponse<EmailResponseDto> afterSchoolCreatedOrEdited(@RequestBody SchoolNotificationResponseDto request);

	// SMS
	@PostMapping("/v1/api/notification/sms")
	public LMSResponse<SmsAlertBody> sendSms(@RequestBody SmsAlertBody smsAlertBody);
	
	@PutMapping("/v1/api/notification/sms_configurations")
	public LMSResponse<Boolean> sendSMSToUser(@Valid @RequestBody SmsGatewayRequestDto request);
	
	@PutMapping("/v1/api/notification/sms/send")
	public LMSResponse<String> sendSmsUser(@RequestBody SmsSendResponseDTO smsSendResponseDTO);

	@PostMapping("/v1/api/notification/emails/createSMS")
	public void sendEmailUser(SmsSendResponseDTO smsSendResponseDTO);

}
