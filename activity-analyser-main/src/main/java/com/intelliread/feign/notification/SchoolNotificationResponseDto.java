package com.intelliread.feign.notification;

import com.intelliread.projection.SchoolsProjection;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Send email after create or edit the school.
 * 
 * <AUTHOR>
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class SchoolNotificationResponseDto {

	private String toEmail;
	private String signatoryName;
	private String name;
	private String roleNameOfAdmin;
	private String adminName;
	private String createdAt;
	private String modifiedAt;
	private String baseFEUrl;
	private String typeOfOperation;
	private String createOrModifiedBy;

	public SchoolNotificationResponseDto(SchoolsProjection projection) {
		this.toEmail = projection.getPocEmail();
		this.signatoryName = projection.getSignatoryName();
		this.name = projection.getName();
		this.createdAt = projection.getCreatedOn();
		this.modifiedAt = projection.getModifiedOn();
		this.createOrModifiedBy = projection.getCreateOrModifiedBy();
	}
}
