package com.intelliread.feign.student;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnitQuizPerformanceMinResponseDto {

	private String quizId;

	private List<SectionObtainedMarksResponseDto> sectionObtainedMarksResponseDto;

	private Integer totalObtainedMarks;

	private Integer studentCount;
}
