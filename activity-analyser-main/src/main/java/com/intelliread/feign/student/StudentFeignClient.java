package com.intelliread.feign.student;

import com.intelliread.config.FiegnConfiguration;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.models.StudentsWithQuizzes;
import com.intelliread.request.dto.StudentExamReqDto;
import com.intelliread.request.dto.StudentExamRequestDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@FeignClient(name = "STUDENT-SERVICE", configuration = FiegnConfiguration.class, url="${api.student.service.url}")
public interface StudentFeignClient {

	// counters
	@GetMapping("/v1/api/student/counters/branch")
	public LMSResponse<List<Long>> branchIds(@RequestParam(value = "branchId", required = true) String branchId);

	@GetMapping("/v1/api/student/counters/student")
	public LMSResponse<List<Long>> studentIds(@RequestParam(value = "studentId", required = true) String studentId);

	@GetMapping("/v1/api/student/examination/student-count")
	public LMSResponse<Long> getStudentsCount(@RequestParam(value = "studentId", required = true) String studentId);

	// quiz
	@GetMapping("/v1/api/student/quiz/has-attended")
	public LMSResponse<Boolean> getQuizAttendOrNot(@RequestParam("studentId") String studentId);

	@PutMapping("/v1/api/student/examination/attended-students")
	public LMSResponse<List<String>> getStudentsExamAttendedORNot(@Valid @RequestBody StudentExamRequestDto request);

	@PutMapping("/v1/api/student/examination/section-transfer")
	public LMSResponse<String> handleStudentsSectionTransfer(@RequestBody StudentExamReqDto request);

	// examination
	@GetMapping("/v1/api/student/examination/student-quiz-obtainedMarks")
	public LMSResponse<List<StudentLevelMinResponseDto>> getStudentsAttendedQuizAndObtainedMarks(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "sectionId", required = false) List<String> sectionList,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);

	@GetMapping("/v1/api/student/examination/count-and-last-submission")
	public LMSResponse<List<StudentQuizCountSubmitDateResoponseDto>> getTheLastSubmissionDateAndCount(
			@RequestParam("students") List<String> students);

	@GetMapping("/v1/api/student/examination/student-quiz-obtained-marks")
	public LMSResponse<UnitQuizPerformanceMinResponseDto> getUnitQuizTotalObtainedMarks(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subtopicId", required = false) String subTopicId,
			@RequestParam(value = "quizId", required = true) String quizId,
			@RequestParam(value = "sectionId", required = false) List<String> sectionIds);

	@GetMapping("/v1/api/student/examination/quiz-attempted/student-count")
	public LMSResponse<List<StudentCountAndObtainedMarksResponseDto>> getStudentCountByReleasedId(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subtopicId", required = false) String subTopicId,
			@RequestParam(value = "releaseIds", required = false) List<String> releaseIds);

	@GetMapping("/v1/api/student/examination/students-quiz-obtainedMarks/teacher-dashboard")
	public LMSResponse<List<QuizReleaseObtainedMarksResponseDto>> getQuizzezAndTotalMarks(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "quizId", required = true) List<String> quizIds);

	@GetMapping("/v1/api/student/examination/students-unit-quiz-obtainedMarks/teacher-dashboard")
	public LMSResponse<List<QuizReleaseObtainedMarksResponseDto>> getUnitQuizzesAndTotalMarks(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "quizId", required = true) List<String> quizIds);

	@PutMapping("/v1/api/student/examination/obtained-marks/selected/students")
	public LMSResponse<List<StudentsWithQuizzes>> obtainedMarkOfSelectedStudents(
			@RequestBody List<StudentsWithQuizzes> request);

	// score range
	@GetMapping("/v1/api/student/score-range/all")
	public LMSResponse<List<ScoreRangeResponseDto>> getAllScoreRanges();

	@GetMapping("/v1/api/student/score-range/ember")
	public LMSResponse<ScoreRangeResponseDto> getEmberScoreRange();

	@GetMapping("/v1/api/student/examination/unit-practice-quiz/obtained-marks")
	public LMSResponse<List<UnitPracticeQuizPerformanceMinResponseDto>> getUnitPracticeQuizTotalObtainedMarks(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "releasedIds", required = false) List<String> releasedIds,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "subjectId", required = false) String subjectId);

	@GetMapping("/v1/api/student/examination/quiz-attempted/subject-wise/list")
	public LMSResponse<UnitPracticeQuizAttemtedResponseDto> getUnitPracticeQuizzesForPrincipal(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subtopicId", required = false) String subTopicId,
			@RequestParam(value = "releaseIds", required = false) List<String> releaseIds,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);
	
	@GetMapping("/v1/api/student/examination/unit-quiz/obtained-marks")
	public LMSResponse<UnitPracticeQuizPerformanceMinResponseDto> getUnitQuizPerformance(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "releasedIds", required = false) List<String> releasedIds,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId);
	
	
	@GetMapping("/v1/api/student/examination/chapter-quiz-overview-report")
	public LMSResponse<TeacherReportStudentQuizResponseDto> getChapterQuizOverViewReport(
			@RequestParam(value = "quizId", required = true) String quizId,
			@RequestParam(value = "studentIds", required = true) List<String> studentIds,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId,
			@RequestParam(value = "startDate", required = false) String startDate,
			@RequestParam(value = "startTime", required = false) String startTime,
			@RequestParam(value = "endDate", required = false) String endDate,
			@RequestParam(value = "endTime", required = false) String endTime);
	
	
	 @PostMapping("/v1/api/student/examination/count-and-last-submission")
	 public LMSResponse<List<StudentQuizCountSubmitDateResoponseDto>> getTheLastSubmissionDateAndCountByIds(
	        @RequestBody List<String> students);
	 
	 @PostMapping("/v1/api/student/examination/chapter-quiz-overview-report")
	 public LMSResponse<TeacherReportStudentQuizResponseDto> getChapterQuizOverViewReportV2(
				@RequestParam(value = "quizId", required = true) String quizId,
				@RequestBody List<String> studentIds, // Use @RequestBody for studentIds
				@RequestParam(value = "schoolId", required = true) String schoolId,
				@RequestParam(value = "branchId", required = true) String branchId,
				@RequestParam(value = "boardId", required = true) String boardId,
				@RequestParam(value = "gradeId", required = true) String gradeId,
				@RequestParam(value = "subjectId", required = true) String subjectId,
				@RequestParam(value = "academicYearId", required = false) String academicYearId,
				@RequestParam(value = "sectionId", required = false) String sectionId,
				@RequestParam(value = "subtopicId", required = false) String subtopicId,
				@RequestParam(value = "startDate", required = false) String startDate,
				@RequestParam(value = "startTime", required = false) String startTime,
				@RequestParam(value = "endDate", required = false) String endDate,
				@RequestParam(value = "endTime", required = false) String endTime);

}
