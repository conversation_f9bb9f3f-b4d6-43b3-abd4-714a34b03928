package com.intelliread.feign.student;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class ScoreRangeResponseDto {

	private String id;

	private int startRange;

	private int endRange;

	private String name;

	private String rangeName;

	private int scoreOrder;

	private boolean active;
	
	public ScoreRangeResponseDto(int startRange, int endRange, String name) {
		this.startRange = startRange;
		this.endRange = endRange;
		this.name = name;
	}

}
