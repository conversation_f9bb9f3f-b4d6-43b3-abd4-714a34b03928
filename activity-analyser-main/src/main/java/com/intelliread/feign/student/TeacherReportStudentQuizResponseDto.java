package com.intelliread.feign.student;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class TeacherReportStudentQuizResponseDto {

	private Long studentCompletedQuiz;

	private Long studentAttendedQuiz;
	
	private String averageTimeTaken;

	private String averageTimeTakenQuestion;
	
	public TeacherReportStudentQuizResponseDto(Long studentCompletedQuiz, Long studentAttendedQuiz) {
		this.studentCompletedQuiz = studentCompletedQuiz;
		this.studentAttendedQuiz = studentAttendedQuiz;
	}
}
