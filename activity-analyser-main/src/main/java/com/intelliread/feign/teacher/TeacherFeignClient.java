package com.intelliread.feign.teacher;

import com.intelliread.response.assignedteacher.ChapterTrackingResponseDto;
import com.intelliread.response.assignedteacher.PaginatedResponse;
import com.intelliread.config.FiegnConfiguration;
import com.intelliread.response.TeachStatusResponseVO;
import com.intelliread.response.dto.TeacherReportQuizOverviewCardResponseDto;
import com.intelliread.response.models.LMSResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;

@FeignClient(name = "TEACHER-SERVICE", configuration = FiegnConfiguration.class, url="${api.teacher.service.url}")
public interface TeacherFeignClient {

	// teach-status controller
	@GetMapping("/v1/api/teacher/teach/chapter/checking/chapter-mapping/{chapterId}")
	public LMSResponse<Boolean> checkTheMappingForChapterId(@PathVariable("chapterId") String chapterId);

	@GetMapping("/v1/api/teacher/teach/chapter/checking/grade-mapping/{gradeId}")
	public LMSResponse<Boolean> checkTheMappingForGrade(@PathVariable("gradeId") String gradeId);

	@GetMapping("/v1/api/teacher/teach/chapter/checking/subject-mapping/{subjectId}")
	public LMSResponse<Boolean> checkTheMappingForSubject(@PathVariable("subjectId") String gradeId);

	@GetMapping("/v1/api/teacher/teach/chapter/checking/sub-topic-mapping/{subTopicId}")
	public LMSResponse<Boolean> checkTheMappingForSubTopic(@PathVariable("subTopicId") String subTopicId);

	@GetMapping("/v1/api/teacher/teach/chapter/start-end/list")
	public LMSResponse<List<String>> startedOrEndedChapterList(
			@RequestParam(value = "subTopicIds", required = false) String subTopicIds,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "sectionId", required = false) String sectionId);

	@GetMapping("/v1/api/teacher/teach/chapter/start-end/ids")
	public LMSResponse<List<String>> startedOrEndedChapterIds(
			@RequestParam(value = "subtopicId", required = false) String subtopicId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "sectionId", required = false) String sectionId);
	
	@GetMapping("/v1/api/teacher/teach/chapter/chapter_start_end_date")
	public LMSResponse<List<TeachStatusResponseVO>> getStartandEndDateDetails(
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "chapterId", required = true) String chapterId);
	
	@GetMapping("/v1/api/teacher/teach/chapter/completed-chapters")
	public LMSResponse<List<String>> completedChapterList(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subTopicId", required = false) List<String> subTopicIds,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "teacherId", required = false) String teacherId);
	
	
	@GetMapping("/v1/api/teacher/teach/chapter/tracking")
	public LMSResponse<PaginatedResponse<ChapterTrackingResponseDto>> getChapterTracking(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "100") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "active", required = false) Boolean active);
	
	
	@GetMapping("/v1/api/teacher/report/quiz-overview-report")
	public LMSResponse<TeacherReportQuizOverviewCardResponseDto> getTeacherReportCardQuizOverview(
			@RequestParam(value = "teacherId", required = false) String teacherId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId,
			@RequestParam(value = "chapterId", required = true) String chapterId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);
	
	@GetMapping("/v1/api/teacher/teach/chapter/teacherFalseTracking")
	public LMSResponse<PaginatedResponse<ChapterTrackingResponseDto>> getChapterteacherFalseTracking(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "100") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "active", required = false) Boolean active);
}
