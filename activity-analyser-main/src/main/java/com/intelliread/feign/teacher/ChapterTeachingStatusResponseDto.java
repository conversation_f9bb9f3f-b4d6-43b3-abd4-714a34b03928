package com.intelliread.feign.teacher;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class ChapterTeachingStatusResponseDto {

	private String gradeId;

	private String subjectId;

	private String chapterId;

	private String sectionId;
	
	private String subTopicId;

	private LocalDate startDate;

	private LocalDate endDate;

	private Integer numberOfDays;

}
