package com.intelliread.feign.content;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentChapterLinkMinResponseDto {
	
	private String chapterId;
	private String chapter;
	private String reviewStatus;
	List<StudentCourseContentMinResponseDto> contentTypes;
	private boolean active;
	
}
