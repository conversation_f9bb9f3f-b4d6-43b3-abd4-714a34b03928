package com.intelliread.feign.content;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class ChapterContentCountResponseDto {

	private String chapterId;

	private long teacherUploaded;

	private long studentStudyUploaded;

	private long studentRevisionUploaded;

	private long teacherPending;

	private long studentStudyPending;

	private long studentRevisionPending;

	// --- optional counts
	private long teacherApproved;

	private long studentStudyApproved;

	private long studentRevisionApproved;

	private long teacherRejected;

	private long studentStudyRejected;

	private long studentRevisionRejected;
}
