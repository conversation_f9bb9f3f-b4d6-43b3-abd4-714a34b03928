package com.intelliread.feign.content;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadContentsResponse {

	private String id;

	private String contentWithIndux;

	private String contentUrl;

	private String reviewStatus;

	private String uploadTime;

	private boolean active;

	private String actualDuration;

	private String pasuedOn;

	private String remainingTime;

	private String watchingCompleted;

	private String lastWatchedOn;

	private String approvedBy;

	private String approvedAt;

	private String rejectedBy;

	private String rejectedAt;

	private String reason;
	
	private DocumentCategoriesResponse documentCategories;

}
