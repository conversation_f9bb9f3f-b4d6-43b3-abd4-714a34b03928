package com.intelliread.feign.content;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChapterDataResponse {

	private String chapterId;

	private List<UploadContentsResponse> teacherUploadContents;
	
	private List<UploadContentsResponse> studentStudyUploadContents;
	
	private List<UploadContentsResponse> studentRevisionUploadContents;
}