package com.intelliread.feign.content;

import com.intelliread.config.FiegnConfiguration;
import com.intelliread.response.dto.*;
import com.intelliread.response.models.LMSResponse;
import com.intelliread.response.quiz.dto.QuizResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

@FeignClient(name = "CONTENT-SERVICE", configuration = FiegnConfiguration.class, url="${api.content.service.url}")
public interface ContentFeignClient {

	// teacher-content
	@GetMapping("/v1/api/content/teacher-chapter/duplicate/content-url")
	public LMSResponse<Boolean> checkTheContentUrlExist(@RequestParam(value = "fileName") String fileName);

	// student-content
	@GetMapping("/v1/api/content/student-chapter/duplicate/content-url")
	public LMSResponse<Boolean> checkTheContentUrlExistForStudents(@RequestParam(value = "fileName") String fileName);

	@GetMapping("/v1/api/content/teacher-chapter/feigncall")
	public LMSResponse<List<com.intelliread.response.teachercontent.ChapterDataResponse>> getUploadContentDetails();

	@GetMapping("/v1/api/content/concepts/subConcepts/rootConcepts/subjects/feign")
	public LMSResponse<List<SubjectsJsonDto>> getSubjectsJson();

	@GetMapping("/v1/api/content/quiz/quiz-feign")
	public LMSResponse<List<QuizResponse>> getQuizDetails(@RequestParam(value = "showCorrectAnswer") boolean showCorrectAnswer);

	@GetMapping("/v1/api/content/quiz/quizId-feign")
	public LMSResponse<QuizResponse> getQuizDetailsById(@RequestParam(value = "quizId") String quizId);

	@GetMapping("/v1/api/content/student-chapter/{chapterId}")
	public LMSResponse<StudentChapterLinkMinResponseDto> getStudentChaptersByChapterId(
			@PathVariable("chapterId") String chapterId);

	// dash-board
	@GetMapping("/v1/api/content/dash-board/content-upload/count/by-chapters")
	public LMSResponse<List<ChapterContentCountResponseDto>> getCountForDashBoardByChapterIds(
			@RequestParam(value = "chapterId") Set<String> chapterId);

	@GetMapping("/v1/api/content/dash-board/content-upload/total/count")
	public LMSResponse<ChapterContentTotalCountResponseDto> getTotalUploadContentToDashBoard(
			@RequestParam(value = "chapterId", required = false) Set<String> chapterId);

	@GetMapping("/v1/api/content/dash-board/content-upload/total/rejected-file")
	public LMSResponse<List<Long>> countOfContentUploadByReviewStatusAndChapterId(
			@RequestParam(value = "chapterId") String chapterId,
			@RequestParam(value = "reviewStatus") String reviewStatus);

	// concept
	@GetMapping("/v1/api/content/concept/chapter-mappings/{chapterId}")
	public LMSResponse<List<ConceptMinResponseDto>> getChapterMappings(@PathVariable("chapterId") String chapterId);

	// quiz
	@GetMapping("/v1/api/content/quiz/chapter-mappings/{chapterId}")
	public LMSResponse<List<QuizMinResponseDto>> getChapterMappingsForQuiz(@PathVariable("chapterId") String chapterId);

	// quiz release
	@GetMapping("/v1/api/content/quiz-release/count")
	public LMSResponse<Integer> getQuizReleaseCount(@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "quizTypeId", required = false) String quizTypeId);

	@GetMapping("/v1/api/content/quiz-release/last-modified-at")
	public LMSResponse<Long> getQuizReleaseLastModifiedAt(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "quizTypeId", required = false) String quizTypeId);

	// student upload content
	@GetMapping("/v1/api/content/student-chapter/chapter-mappings/{chapterId}")
	public LMSResponse<List<String>> getStudentUploadMappingsForChapterId(@PathVariable("chapterId") String chapterId);

	// teacher upload content
	@GetMapping("/v1/api/content/teacher-chapter/chapter-mappings/{chapterId}")
	public LMSResponse<List<String>> getTeacherUploadMappingsForChapterId(@PathVariable("chapterId") String chapterId);

	@GetMapping("/v1/api/content/blueprint/check_mappings/{paperTypeId}")
	public LMSResponse<Boolean> getMappingOfPaperType(@PathVariable("paperTypeId") String paperTypeId);

	// document-category
	@GetMapping("/v1/api/content/document-category/count-role-id")
	public LMSResponse<Long> countRoleIdForDeletion(@RequestParam("roleId") String roleId);

	// misc-controller
	@GetMapping("/v1/api/content/misc/checking/board-mapping/{boardId}")
	public LMSResponse<Boolean> checkTheMappingForBoardId(@PathVariable("boardId") String boardId);

	@GetMapping("/v1/api/content/misc/checking/chapter-mapping/{chapterId}")
	public LMSResponse<Boolean> checkTheMappingForChapterId(@PathVariable("chapterId") String chapterId);

	@GetMapping("/v1/api/content/misc/checking/grade-mapping/{gradeId}")
	public LMSResponse<Boolean> checkTheMappingForGradeId(@PathVariable("gradeId") String gradeId);

	@GetMapping("/v1/api/content/misc/checking/subject-mapping/{subjectId}")
	public LMSResponse<Boolean> checkTheMappingForSubject(@PathVariable("subjectId") String subjectId);

	@GetMapping("/v1/api/content/misc/checking/sub-topic-mapping/{subTopicId}")
	public LMSResponse<Boolean> checkTheMappingForSubTopic(@PathVariable("subTopicId") String subTopicId);

	@GetMapping("/v1/api/content/misc/checking/academic-year-id-mapping/{academicYearId}")
	public LMSResponse<Boolean> checkTheMappingForAcademicYearId(@PathVariable("academicYearId") String academicYearId);

	@GetMapping("/v1/api/content/misc/list-of-chapter")
	public LMSResponse<List<String>> getAllTheChaptersFromTeacherAndStudents(
			@RequestParam("reviewStatus") String reviewStatus);

	// Quiz
	@GetMapping("/v1/api/content/quiz/count-by-chapter")
	public LMSResponse<List<ChapterQuizCountResponseDto>> quizCountBasedOnChapters(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "quizTypeId", required = false) String quizTypeId,
			@RequestParam(value = "chapterIds", required = true) List<String> chapterIds);

	@GetMapping("/v1/api/content/quiz/quizes-count")
	public LMSResponse<Long> quizesCountForRevisionReviewer(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "quizTypeId", required = false) String quizTypeId,
			@RequestParam(value = "chapterIds", required = true) List<String> chapterIds);

	@GetMapping("/v1/api/content/concepts/get-subconcept-details")
	public LMSResponse<List<SubTopicSubConceptDetailsDto>> getSubConceptDetails(
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "studentId", required = true) String studentId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "chapters", required = true) List<String> chapters);

	@GetMapping("/v1/api/content/concepts/get-chapter-details-score")
	public LMSResponse<Double> getChapterScore(@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "studentId", required = true) String studentId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "chapterId", required = false) String chapterId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId);

	@GetMapping("/v1/api/content/concepts/get-subconcept-details-practice-quiz")
	public LMSResponse<List<SubTopicSubConceptDetailsDto>> getSubConceptDetailsPractice(
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "studentId", required = true) String studentId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);

	@GetMapping("/v1/api/content/concepts/get-practice-score")
	LMSResponse<PracticeQuizScoreDetails> getPracticeQuizScore(
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "studentId", required = true) String studentId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "chapterId", required = false) String chapterId);

	@GetMapping("/v1/api/content/concepts/get-diagnostic-quiz-subconcept-detail")
	LMSResponse<DiagnosticQuizOverAllResponseDto> getSubConceptDetailsDiagnosticQuiz(
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "studentId", required = true) String studentId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId,
			@RequestParam(value = "quizId", required = true) String quizId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);

	@GetMapping("/v1/api/content/concepts/get-diagnostic-quiz-subtopic-detail")
	LMSResponse<SubTopicSubConceptDto> getSubTopicScoreDetails(
			@RequestParam(value = "subTopicId", required = true) String subTopicId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "studentId", required = true) String studentId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId,
			@RequestParam(value = "quizId", required = true) String quizId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "chapters", required = false) List<String> chapters);

	@GetMapping("/v1/api/content/concepts/get-list-chapter-exam-end")
	public LMSResponse<List<QuizExaminationMarkDetails>> getChaptersListStatusEnd(
			@RequestParam(value = "chapterIds", required = true) List<String> chapterIds,
			@RequestParam(value = "subtopicId", required = false) String subtopicId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "studentId", required = true) String studentId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "sectionId", required = true) String sectionId);

	@GetMapping("/v1/api/content/concepts/get-quiz-score")
	public LMSResponse<Double> getQuizScore(@RequestParam(value = "quizId", required = true) String quizId,
			@RequestParam(value = "studentId", required = true) String studentId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "sectionId", required = true) String sectionId);

	@GetMapping("/v1/api/content/concepts/get-chapter-details-score-attend")
	public LMSResponse<Double> getChapterScoreAttendDetails(
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "studentId", required = true) String studentId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "chapterId", required = false) String chapterId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId);

	// Blueprint-Levels
	@GetMapping("/v1/api/content/blueprint-level/with-or-without/ids")
	public LMSResponse<List<BlueprintLevelResponseDto>> allBlueprintLevelsWithOrWithoutIds(
			@RequestParam(value = "ids", required = false) List<String> ids);
	
	@GetMapping("/v1/api/content/student-chapter/feign/{chapterId}")
	public LMSResponse<StudentUploadContentResponse> getDocumentDetailsByChapterId(
			@PathVariable("chapterId") String chapterId);
	
	@GetMapping("/v1/api/content/teacher-chapter/feign/{chapterId}")
	public LMSResponse<ChapterDataResponse> getTeacherDocumentDetailsByChapterId(
			@PathVariable("chapterId") String chapterId);
	
	@GetMapping("/v1/api/content/teacher-chapter/feigncall/{chapterId}")
	public LMSResponse<ChapterDataResponse> getUploadContentDetailsByChapterId(
			@PathVariable("chapterId") String chapterId);
	
	
	// quiz release
		@GetMapping("/v1/api/content/quiz-release/release-count")
		public LMSResponse<Integer> quizReleaseCount(@RequestParam(value = "boardId", required = false) String boardId,
				@RequestParam(value = "schoolId", required = false) String schoolId,
				@RequestParam(value = "branchId", required = false) String branchId,
				@RequestParam(value = "gradeId", required = false) String gradeId,
				@RequestParam(value = "sectionId", required = false) String sectionId,
				@RequestParam(value = "academicYearId", required = false) String academicYearId,
				@RequestParam(value = "subjectId", required = false) String subjectId,
				@RequestParam(value = "subTopicId", required = false) String subTopicId,
				@RequestParam(value = "quizTypeId", required = false) String quizTypeId);
	
}
