package com.intelliread.feign.content;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentUploadContentResponse {

	private String chapterId;

	private String studyDocWithCount;

	private List<StudyDocumentCategoriesResponse> studyDocumentCategories;

	private String revisionDocWithCount;

	private List<RevisionDocumentCategoriesResponse> revisionDocumentCategories;

}
