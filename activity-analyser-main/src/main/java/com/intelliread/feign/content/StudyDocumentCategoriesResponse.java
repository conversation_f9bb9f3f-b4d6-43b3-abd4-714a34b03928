package com.intelliread.feign.content;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudyDocumentCategoriesResponse {
	
	private String documentType;
	
	private String docTypeWithCount;
	
	private String documentUrl;
	
	private List<StudentStudyContentResponse> studentStudyContent;
	
	private String documentCategoryId;
	
	
}
