package com.intelliread.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.intelliread.component.Translator;
import com.intelliread.enums.ErrorCodes;
import com.intelliread.exception.USException;
import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.CsvBindByPosition;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;

@Data
@Slf4j
public class StudentsFile {

	@CsvBindByName(column = "firstName", required = true)
	@CsvBindByPosition(position = 0)
	private String firstName;

	@CsvBindByName(column = "lastName", required = true)
	@CsvBindByPosition(position = 1)
	private String lastName;

	@CsvBindByName(column = "email")
	@CsvBindByPosition(position = 2)
	private String email;

	@CsvBindByName(column = "mobile", required = true)
	@CsvBindByPosition(position = 3)
	private String mobile;

	@CsvBindByName(column = "gender")
	@CsvBindByPosition(position = 4)
	private String gender;

	@CsvBindByName(column = "dob")
	@CsvBindByPosition(position = 5)
	private String dob;

	@CsvBindByName(column = "firstLanguage")
	@CsvBindByPosition(position = 6)
	private String firstLanguage;

	@CsvBindByName(column = "secondLanguage")
	@CsvBindByPosition(position = 7)
	private String secondLanguage;

	@CsvBindByName(column = "admissionDate")
	@CsvBindByPosition(position = 8)
	private String admissionDate;

	@CsvBindByName(column = "grade", required = true)
	@CsvBindByPosition(position = 9)
	private String grade;

	@CsvBindByName(column = "section")
	@CsvBindByPosition(position = 10)
	private String section;

	@CsvBindByName(column = "studentCategory")
	@CsvBindByPosition(position = 11)
	private String studentCategory;

	@CsvBindByName(column = "address")
	@CsvBindByPosition(position = 12)
	private String address;
	
	@CsvBindByName(column = "schoolCode")
	@CsvBindByPosition(position = 13)
	private String schoolCode;
	
	@CsvBindByName(column = "branchName")
	@CsvBindByPosition(position = 14)
	private String branchName;
	
	@JsonInclude(value = Include.NON_NULL)
	private StringBuilder remarks;
	
	private boolean failed;
	
	/**
	 * Using the reflection checking the fields whether it is empty or not. Here
	 * validation annotation is not using, but all the fields are mandatory.
	 * 
	 * removing the grade and section from the mandatory section, will add it later
	 * && value.equals("gradeId") && value.equals("sectionId")
	 * 
	 * @return
	 */
	@JsonIgnore
	public boolean isNull() {
		Field[] fields = this.getClass().getDeclaredFields();
		for (Field f : fields) {
			try {
				String fieldName = f.getName();
				Object value = f.get(this);
				log.info("value: " + value);

				if ( (fieldName.equals("firstName") && StringUtils.isBlank((CharSequence) value)) ||
						(fieldName.equals("lastName") && StringUtils.isBlank((CharSequence) value)) ||
						(fieldName.equals("mobile") && StringUtils.isBlank((CharSequence) value)) ||
						(fieldName.equals("gender") && StringUtils.isBlank((CharSequence) value)) ||
						//(fieldName.equals("email") && StringUtils.isBlank((CharSequence) value)) ||
						(fieldName.equals("grade") && StringUtils.isBlank((CharSequence) value)) ||
						(fieldName.equals("schoolCode") && StringUtils.isBlank((CharSequence) value)) ||
						(fieldName.equals("branchName") && StringUtils.isBlank((CharSequence) value))) {
					throw new USException(ErrorCodes.BAD_REQUEST,
							"firstName, lastName, mobile, gender, email, grade, schoolCode, branchName "
									+ Translator.toLocale("are.mandatory.fields", null));
				}
			} catch (IllegalArgumentException | IllegalAccessException e) {
				e.printStackTrace();
			}
		}
		return false;
	}
}