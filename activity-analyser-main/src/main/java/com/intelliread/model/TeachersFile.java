package com.intelliread.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.intelliread.component.Translator;
import com.intelliread.enums.ErrorCodes;
import com.intelliread.exception.USException;
import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.CsvBindByPosition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;

@AllArgsConstructor
@Data
@NoArgsConstructor
@Slf4j
public class TeachersFile {

	@CsvBindByPosition(position = 0)
	@CsvBindByName(column = "firstName", required = true)
	private String firstName;

	@CsvBindByPosition(position = 1)
	@CsvBindByName(column = "lastName", required = true)
	private String lastName;

	@CsvBindByPosition(position = 2)
	@CsvBindByName(column = "email", required = true)
	private String email;

	@CsvBindByPosition(position = 3)
	@CsvBindByName(column = "mobile", required = true)
	private String mobile;

	@CsvBindByPosition(position = 4)
	@CsvBindByName(column = "gender", required = true)
	private String gender;

	@CsvBindByPosition(position = 5)
	@CsvBindByName(column = "dob")
	private String dob;

	@CsvBindByPosition(position = 6)
	@CsvBindByName(column = "joinDate")
	private String joinDate;

	@CsvBindByPosition(position = 7)
	@CsvBindByName(column = "previousWorkExp")
	private String previousWorkExp;

	@CsvBindByPosition(position = 8)
	@CsvBindByName(column = "address")
	private String address;

	@CsvBindByPosition(position = 9)
	@CsvBindByName(column = "designation")
	private String designation;

	@CsvBindByPosition(position = 10)
	@CsvBindByName(column = "schoolCode")
	private String schoolCode;

	@CsvBindByPosition(position = 11)
	@CsvBindByName(column = "branchName")
	private String branchName;

	@JsonInclude(value = Include.NON_NULL)
	private StringBuilder remarks;
	
	private boolean failed;

	/**
	 * Using the reflection checking the fields whether it is empty or not. Here
	 * validation annotation is not using, but all the fields are mandatory.
	 * 
	 * @return
	 */
	@JsonIgnore
	public boolean isNull() {
		Field[] fields = this.getClass().getDeclaredFields();
		for (Field f : fields) {
			try {
				// retrieves the name for the field
				String fieldName = f.getName();

				// retrieves the value for the field
				Object value = f.get(this);

				log.info("value: " + value);
				if ((fieldName.equals("firstName") && StringUtils.isBlank((CharSequence) value))
						|| (fieldName.equals("lastName") && StringUtils.isBlank((CharSequence) value))
						|| (fieldName.equals("mobile") && StringUtils.isBlank((CharSequence) value))
						|| (fieldName.equals("gender") && StringUtils.isBlank((CharSequence) value))
						|| (fieldName.equals("email") && StringUtils.isBlank((CharSequence) value))
						//Validation stoped 05_april_2024
						//|| (fieldName.equals("dob") && StringUtils.isBlank((CharSequence) value))
						//|| (fieldName.equals("joinDate") && StringUtils.isBlank((CharSequence) value))
						//|| (fieldName.equals("previousWorkExp") && StringUtils.isBlank((CharSequence) value))
						|| (fieldName.equals("schoolCode") && StringUtils.isBlank((CharSequence) value))
						|| (fieldName.equals("branchName") && StringUtils.isBlank((CharSequence) value))) {

//                  Validation stoped 05_april_2024
//					throw new USException(ErrorCodes.BAD_REQUEST,
//							"firstName, lastName, mobile, gender, email, dob, joinDate, previousWorkExp, schoolCode, branchName "
//									+ Translator.toLocale("are.mandatory.fields", null));
					
					throw new USException(ErrorCodes.BAD_REQUEST,
							"firstName, lastName, mobile, gender, email, schoolCode, branchName "
									+ Translator.toLocale("are.mandatory.fields", null));
				}
			} catch (IllegalArgumentException | IllegalAccessException e) {
				e.printStackTrace();
			}
		}
		return false;
	}
}
