package com.intelliread.model;

import com.intelliread.entity.master.SpeakAloud;
import jakarta.validation.constraints.*;

import java.util.List;

public class Activity {
    @NotNull(message = "Assignee ID is required.")
    private String assigneeId;

    @NotNull(message = "Course ID is required.")
    private String courseId;

    @NotNull(message = "Section ID is required.")
    private String sectionId;

    @NotNull(message = "Chapter ID is required.")
    private String chapterId;

    @NotNull(message = "End time is required.")
    private String endTime;

    @NotNull(message = "Start time is required.")
    private String startTime;

    @NotNull(message = "Type is required.")
    private String type;

    private int assessmentScore;

    @Size(min = 1, message = "Speak Aloud must have at least one entry if present.")
    private List<SpeakAloud> speakAloud;

    // Getters and Setters
    public String getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(String assigneeId) {
        this.assigneeId = assigneeId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getSectionId() {
        return sectionId;
    }

    public void setSectionId(String sectionId) {
        this.sectionId = sectionId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setAssessmentScore(int score) { this.assessmentScore = score; }

    public int getAssessmentScore() { return (assessmentScore != 0) ? assessmentScore : 0; }

    public List<SpeakAloud> getSpeakAloud() {
        return speakAloud;
    }

    public void setSpeakAloud(List<SpeakAloud> speakAloud) {
        this.speakAloud = speakAloud;
    }
}
