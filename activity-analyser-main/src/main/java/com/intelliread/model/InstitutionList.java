package com.intelliread.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class InstitutionList {

	private String schoolId;

	private String branchId;

	private long totalStudents;

	// Key = quizId and value = total mark of the quiz
	private HashMap<String, Integer> unitQuizList;

	private HashMap<String, Integer> practiceQuizList;

	public InstitutionList(String schoolId, String branchId, long totalStudents) {
		this.schoolId = schoolId;
		this.branchId = branchId;
		this.totalStudents = totalStudents;
	}

}
