package com.intelliread.mapper;

import com.intelliread.entity.users.Students;
import com.intelliread.projection.StudentsProjection;
import com.intelliread.response.dto.StudentsResponseDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> | April 9 2022
 *
 * @implNote Interface for mapping of various response, request models to and
 *           from Students entity
 *
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface StudentMapper {

	StudentMapper INSTANCE =  Mappers.getMapper(StudentMapper.class);
	
		 @Mapping(source ="schools.id", target = "schoolId" )
		 @Mapping(source ="schools.name", target = "schoolName" )
		 @Mapping(source ="schools.code", target = "schoolCode" )
		 @Mapping(source = "branches.id", target = "branchId")
		 @Mapping(source = "branches.name", target = "branchName")
	StudentsResponseDto mapStudentToResponseDto(Students student);
	
	List<StudentsResponseDto> mapStudentToResponseDto(List<Students> students);

	//List<StudentsResponseDto> mapStudentProjectionToResponseDto(List<StudentsProjection> studentProjectionList);
	
	StudentsResponseDto mapStudentProjectionToResponseDto(StudentsProjection studentProjection);

}
