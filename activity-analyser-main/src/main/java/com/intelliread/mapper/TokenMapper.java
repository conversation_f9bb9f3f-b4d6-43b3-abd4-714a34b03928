package com.intelliread.mapper;

import com.intelliread.entity.users.Tokens;
import com.intelliread.entity.users.UsersTokenMapping;
import com.intelliread.projection.TokenProjection;
import com.intelliread.projection.TokenUserMappingProjection;
import com.intelliread.response.dto.TokenDetailsResponseDto;
import com.intelliread.response.dto.TokensResponseDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * <AUTHOR> | April 12 2022
 *
 * @implNote Interface for mapping of various response, request models to and
 *           from Tokens entity
 *
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface TokenMapper {

	/**
	 * 
	 * Method declaration for mapping token entity to TokensResponseDto
	 * {@code @Mapping} annotation specifies the mapping required
	 * 
	 * @param token
	 * @return
	 */
	@Mapping(source = "branches.id", target = "branchId")
	@Mapping(source = "branches.name", target = "branchName")
	@Mapping(source = "schools.id", target = "schoolId")
	@Mapping(source = "schools.name", target = "schoolName")
	@Mapping(source = "schools.code", target = "schoolCode")
	@Mapping(source = "userTokenMappings", target = "tokenDetails")
	TokensResponseDto mapTokenEntityToResponse(Tokens token);

	/**
	 * 
	 * Method declaration for mapping token entities to list of TokensResponseDto
	 * 
	 * Internally uses {@code mapTokenEntityToResponse}
	 * 
	 * @param tokens
	 * @return
	 */
	List<TokensResponseDto> mapTokenEntityToResponse(List<Tokens> tokens);

	/**
	 * 
	 * Method declaration for mapping UserTokenMapping entity to
	 * TokenDetailsResponseDto {@code @Mapping} annotation specifies the mapping
	 * required
	 * 
	 * @param userTokenMapping
	 * @return
	 */
	@Mapping(source = "users.id", target = "userId")
	@Mapping(source = "users.email", target = "email")
	@Mapping(source = "users.phoneNumber", target = "phone")
	TokenDetailsResponseDto mapTokenUserMappingToResponseDto(UsersTokenMapping userTokenMapping);

	/**
	 * 
	 * Method declaration for mapping UserTokenMapping entity to
	 * TokenDetailsResponseDto
	 * 
	 * Internally uses {@code mapTokenUserMappingToResponseDto}
	 * 
	 * @param userTokenMappings
	 * @return
	 */
	List<TokenDetailsResponseDto> mapTokenUserMappingToResponseDto(List<UsersTokenMapping> userTokenMappings);

	/**
	 * 
	 * Method declaration for mapping List of TokenProjection to TokensResponseDto
	 *
	 * @param tokenProjection
	 * @return
	 */
	List<TokensResponseDto> mapTokenProjectionToResponse(List<TokenProjection> tokenProjection);

	/**
	 * 
	 * Method declaration for mapping List of TokenUserMappingProjection to
	 * TokenDetailsResponseDto
	 *
	 * @param tokenProjection
	 * @return
	 */
	List<TokenDetailsResponseDto> mapTokenUserMappingToTokenDetailsDto(
			List<TokenUserMappingProjection> tokenUserMappingProjections);
}
