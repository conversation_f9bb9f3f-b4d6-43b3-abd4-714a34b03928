package com.intelliread.mapper;

import com.intelliread.entity.master.PlanGradesMapping;
import com.intelliread.entity.master.PlanTemplate;
import com.intelliread.entity.master.Plans;
import com.intelliread.request.dto.PlanTemplateRequestDto;
import com.intelliread.response.dto.PlanTemplateResponseDto;
import com.intelliread.response.dto.PlansGradesMappingResponseDto;
import com.intelliread.response.dto.PlansResponseDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PlanMapper {
	
	PlanMapper INSTANCE = Mappers.getMapper(PlanMapper.class);

	@Mapping(source ="boards.id", target ="boardId")
	@Mapping(source ="boards.board", target ="board")
	@Mapping(source ="boards.discription", target ="boardDiscription")
	//@Mapping(source = "planGradesMappings", target ="planGrades")
	PlansResponseDto mapPlanEntityToPlanDto(Plans plan);
	
	List<PlansResponseDto> mapPlanEntityToPlanDto(List<Plans> plans);
	
	@Mapping(source ="grades.id", target ="gradeId" )
	@Mapping(source ="grades.grade", target ="grade" )
	//@Mapping(source = "planTemplates", target ="planTemplates")
	PlansGradesMappingResponseDto mapGradeMappingEntityToDto(PlanGradesMapping mapping);
	
	//List<PlansGradesMappingResponseDto> mapGradeMappingEntityToDto(List<PlanGradesMapping> mappings);

	@Mapping(source ="subjects.id", target ="subjectId" )
	@Mapping(source ="subjects.subject", target ="subject" )
	PlanTemplateResponseDto mapPlanTemplateEntityToDto(PlanTemplate mapping);
	
	List<PlanTemplateResponseDto> mapPlanTemplateEntityToDto(List<PlanTemplate> mappings);

	PlanTemplate mapPlanTemplateDtoToEntity(PlanTemplateRequestDto planTemplateDto, @MappingTarget PlanTemplate planTemplate);
	
	@Mapping(source ="id", target ="id" )
	@Mapping(source ="grades.id", target ="gradeId" )
	@Mapping(source ="grades.grade", target ="grade" )
	@Mapping(source ="active", target ="active" )
	PlansGradesMappingResponseDto planGradesMappingEntityToDto(PlanGradesMapping mapping);
}
