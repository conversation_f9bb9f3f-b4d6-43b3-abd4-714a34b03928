package com.intelliread.entity.master;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

/**
 * Mapping the subject with sub-topic, grades and boards
 * Composite key will be: subject, sub-topic, grades and boards.
 * 
 * <AUTHOR>
 * @since 1.0.5
 *
 */
@Getter
@Setter
@ToString
@Entity
@Table(name = "subject_mapping")
public class SubjectMapping extends AuditMetadata {
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "subjects_id")
	private Subjects subjects;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "boards_id")
	private Boards boards;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "grades_id")
	private Grades grades;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "sub_topics_id")
	private SubTopics subTopics;
	
	@Column(name = "skilled_subject", columnDefinition = "boolean default true")
	private boolean skilledSubject;

}
