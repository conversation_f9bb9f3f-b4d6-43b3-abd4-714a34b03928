package com.intelliread.entity.master;

import com.intelliread.enums.LessonPlanStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.Cache;

import jakarta.persistence.*;

@Getter
@Setter
@ToString
@Entity
@Table(name = "lesson_plan")
@Cache(region = "lesson_plan", usage = CacheConcurrencyStrategy.READ_WRITE)
public class LessonPlanEntity extends AuditMetadata {
		
	@Column(name = "course_code")
	private String courseCode;
	
	@Column(name = "course_id")
	private String courseId;
	
	@Enumerated(EnumType.STRING)
	@Column(name = "status")
	private LessonPlanStatus status;
	
	@Column(columnDefinition = "jsonb", name = "lesson_plan")
	@ColumnTransformer(write = "?::jsonb")
	private String lessonPlan;
	
	@Column(name = "assessments")
	private String assessments;
	
	@Column(name = "created_date")
	private String createdDate;
	
	@Column(name = "created_by")
	private String createdBy;
	
	@Column(name = "created_user_name")
	private String createdUserName;
	
	@Column(name = "last_updated_date")
	private String lastUpdatedDate;
	
	@Column(name = "last_updated_by")
	private String lastUpdatedBy;
	
	@Column(name = "last_updated_user_name")
	private String lastUpdatedUserName;
	
	@Column(columnDefinition = "jsonb", name = "update_history")
	@ColumnTransformer(write = "?::jsonb")
	private String updateHistory;

}
