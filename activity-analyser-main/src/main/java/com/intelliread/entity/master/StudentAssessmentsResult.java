package com.intelliread.entity.master;

import lombok.*;
import org.hibernate.annotations.ColumnTransformer;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ir_student_assessment_result")
public class StudentAssessmentsResult extends AuditMetadata {
	
	@Column(name = "assignee_id")
	private String assigneeId;
	
	@Column(name = "assessment_id")
	private String assessmentId;
	
	@Column(columnDefinition = "jsonb", name = "assessments")
	@ColumnTransformer(write = "?::jsonb")
	private String assessments;

}
