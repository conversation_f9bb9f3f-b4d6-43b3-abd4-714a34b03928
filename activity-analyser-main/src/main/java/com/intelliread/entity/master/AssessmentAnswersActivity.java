package com.intelliread.entity.master;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;
import org.hibernate.annotations.ColumnTransformer;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ir_assessment_answers_activity")
public class AssessmentAnswersActivity extends AuditMetadata{

    @Column(name = "assessment_id")
    private String assessmentId;

    @Column(name = "username")
    private String username;

    @Column(columnDefinition = "jsonb", name = "selected_answers")
    @ColumnTransformer(write = "?::jsonb")
    private String selectedAnswers;
}
