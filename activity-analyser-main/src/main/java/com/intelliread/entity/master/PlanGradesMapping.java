package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.validation.constraints.NotNull;


/**
 * <AUTHOR> |  14 April 2022 
 *
 * Maps subjects to a plan
 *
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = false)
@Entity(name = "plan_grades_mapping")
public class PlanGradesMapping extends AuditMetadata {

	@NotNull(message = Constants.MANDATORY_FIELD)
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "plan_id")
	private Plans plan;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "grade_id")
	private Grades grades;	
	
//	@OneToMany(cascade = CascadeType.ALL, mappedBy = "planGradeMapping")
//	private List<PlanTemplate> planTemplates = new ArrayList<>();
}
