package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;

/**
 * The Languages Entity class<br>
 * 
 * The {@code Languages} Entity Class extends {@code AuditMetadata} that returns
 * Audit Details like Created Date, Created By, Last Created Date, Last Modified
 * Date<br>
 * This class is used to store the data by mapping Object to the DB Relation
 * <br>
 * 
 * {@code @Entity} used for identifying Domain Object
 * relating to Database Table: {@code "languages"} <br>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "languages")
@Cache(region = "languages", usage = CacheConcurrencyStrategy.READ_WRITE)
public class Languages extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "language", unique = true)
	private String language;

}
