package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
@ToString
@Entity
@Table(name = "roles")
public class Roles extends AuditMetadata {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.CAP_LTRS_UNDER_SCORE, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "role")
	private String role;
	
	@Column(name = "discription")
	private String discription;
	
	@Column(name = "sort_order", unique = true)
	private Integer sortOrder;
	
	@Column(name = "admin_role", columnDefinition = "boolean default true")
	private boolean adminRole;
}
