package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.*;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "plan_blueprint_level_mapping")
public class PlanBlueprintLevelMapping extends AuditMetadata {
	
	@NotNull(message = Constants.MANDATORY_FIELD)
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "plan_id")
	private Plans plan;
	
	@Column(name = "blueprint_level_id")
	private String blueprintLevelId;
	
}
