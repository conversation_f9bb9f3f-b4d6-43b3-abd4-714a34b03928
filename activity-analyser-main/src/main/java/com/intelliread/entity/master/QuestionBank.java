package com.intelliread.entity.master;

import com.intelliread.request.dto.CategoryEnum;
import com.intelliread.request.dto.QuestionBankStatus;
import com.intelliread.request.dto.TaxonomyEnum;
import com.intelliread.request.dto.TypeEnum;
import lombok.*;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnTransformer;

import jakarta.persistence.*;
import org.hibernate.annotations.Cache;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ir_question_bank")
@Cache(region = "ir_question_bank", usage = CacheConcurrencyStrategy.READ_WRITE)
public class QuestionBank extends AuditMetadata {
	
	@Enumerated(EnumType.STRING)
	@Column(name="status")
	private QuestionBankStatus status;
	
	@Column(name="course_code")
	private String courseCode;
	
	@Column(name="course_id")
	private String courseId;
	
	@Column(name="section_id")
	private String sectionId;
	
	@Column(name="chapter_id")
	private String chapterId;
	
	@Column(columnDefinition = "jsonb", name = "group_details")
	@ColumnTransformer(write = "?::jsonb")
	private String groupDetails;
	
	@Enumerated(EnumType.STRING)
	@Column(name="type")
	private TypeEnum type;
	
	@Enumerated(EnumType.STRING)
	@Column(name="category")
	private CategoryEnum category;
	
	@Enumerated(EnumType.STRING)
	@Column(name="taxonomy")
	private TaxonomyEnum taxonomy;
	
	@Column(columnDefinition = "jsonb", name = "question_details")
	@ColumnTransformer(write = "?::jsonb")
	private String questionDetails;
	
	@Column(columnDefinition = "jsonb", name = "answer_details")
	@ColumnTransformer(write = "?::jsonb")
	private String answerDetails;

	@Column(name="created_date")
	private String createdDate;
	
	@Column(name="created_username")
	private String createdUserName;
	
	@Column(columnDefinition = "jsonb", name = "update_history")
	@ColumnTransformer(write = "?::jsonb")
	private String updateHistory;
	
	@Column(columnDefinition = "jsonb", name = "approval_history")
	@ColumnTransformer(write = "?::jsonb")
	private String approvalHistory;

}
