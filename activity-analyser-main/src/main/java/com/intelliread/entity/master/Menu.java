package com.intelliread.entity.master;

import com.intelliread.enums.PlanFeatures;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

/**
 * <AUTHOR>
 * @since 0.0.1
 *
 */
@Getter
@Setter
@ToString
@Entity
@Table(name = "menu")
public class Menu extends AuditMetadata {

	@Column(name = "menu_name")
	private String menuName;

	@Column(name = "sequence_no")
	private int sequenceNo;
	
	@Enumerated(value = EnumType.STRING)
	@Column(name = "plan_feature", length = 32, columnDefinition = "varchar(32)")
	private PlanFeatures planFeature;
	
	@Column(name = "application")
	private String application;
	
	@Column(name = "icon_class")
	private String iconClass;
	
	@Column(name = "route")
	private String route;

}
