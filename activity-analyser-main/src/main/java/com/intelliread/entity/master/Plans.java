package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@Entity
@Table(name = "plans")
public class Plans extends AuditMetadata {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "plan")
	private String plan;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "discription")
	private String discription;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "board_id")
	private Boards boards;
	
//	@OneToMany(cascade = CascadeType.ALL, mappedBy = "plan")
//	List<PlanGradesMapping> planGradesMappings = new ArrayList<>();
}
