package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
@ToString
@Entity
@Table(name = "academic_years")
public class AcademicYears extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.YEAR_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "starting_year")
	private String startingYear;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.YEAR_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "ending_year")
	private String endingYear;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.ACADEMIC_YEAR, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "academic_year")
	private String academicYear;
}
