package com.intelliread.entity.master;

import com.intelliread.enums.AssessmentsType;
import com.intelliread.request.dto.QuestionBankStatus;
import lombok.*;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.Cache;

import jakarta.persistence.*;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ir_assessments")
@Cache(region = "ir_assessments", usage = CacheConcurrencyStrategy.READ_WRITE)
public class AssessmentsEntity extends AuditMetadata {

	@Enumerated(EnumType.STRING)
	@Column(name = "status")
	private QuestionBankStatus status;

	@Column(name = "course_code")
	private String courseCode;

	@Column(name = "name")
	private String name;

	@Column(name = "course_id")
	private String courseId;

	@Column(name = "topic")
	private String topic;

	@Column(name = "level")
	private String level;

	@Column(name = "section_id")
	private String sectionId;

	@Column(name = "chapter_id")
	private String chapterId;

	@Enumerated(EnumType.STRING)
	@Column(name = "type")
	private AssessmentsType type;

	@Column(name = "time_allowed")
	private String timeAllowed;

	@Column(name = "no_of_questions")
	private String noOfQuestions;

	@Column(name = "total_marks")
	private String totalMarks;

	@Column(name = "grading_model")
	private String gradingModel;

	@Column(name = "pass_grade")
	private String passGrade;

	@Column(name = "is_timed")
	private String isTimed;

	@Column(columnDefinition = "jsonb", name = "questions")
	@ColumnTransformer(write = "?::jsonb")
	private String questions;

	@Column(name = "created_date")
	private String createdDate;

	@Column(name = "created_user_name")
	private String createdUserName;

	@Column(columnDefinition = "jsonb", name = "update_history")
	@ColumnTransformer(write = "?::jsonb")
	private String updateHistory;

//	@ManyToMany
//    @JoinTable(name = "ir_assessments_questions", joinColumns = @JoinColumn(name = "assessment_id"), 
//      	inverseJoinColumns = @JoinColumn(name = "question_id"))
//   private List<QuestionBank> questionss=new ArrayList<>();
//	@ManyToMany(fetch = FetchType.LAZY, cascade = { CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH })
//    @JoinTable(name = "ir_questions_assessments", joinColumns = @JoinColumn(name = "question_id"), 
//        	inverseJoinColumns = @JoinColumn(name = "assessments_id"))
//    private List<QuestionBank> questionBank;

}
