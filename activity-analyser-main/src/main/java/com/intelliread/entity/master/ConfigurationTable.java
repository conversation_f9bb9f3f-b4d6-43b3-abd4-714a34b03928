package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
@ToString
@Entity
@Table(name = "configuration_table", uniqueConstraints = {@UniqueConstraint(columnNames = {"key", "value"})})
@Cache(region = "configuration_table", usage = CacheConcurrencyStrategy.READ_WRITE)
public class ConfigurationTable extends AuditMetadata {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY) // Auto-increment for databases
	@Column(name = "id")
	private String id;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.CAP_LTRS_UNDER_SCORE, message = "Please refer these example: DEV_BASE_URL")
	@Column(name = "key", unique = true)
	private String key;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "value")
	private String value;
}
