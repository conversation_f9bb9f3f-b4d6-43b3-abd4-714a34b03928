package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * Payment represents completed B2C Payment transactions.
 * <p>
 * This entity supports Razorpay-based and cash payment methods,
 * ensuring comprehensive tracking of all completed transactions.
 * Fields include transaction ID, user ID, amount paid, payment date,
 * payment mode, and Razorpay-specific details for online transactions.
 *
 * <AUTHOR>
 * @since 1.0.1
 */

@Getter
@Setter
@ToString
@Entity
@Table(name = "ir_payments")
public class Payment extends AuditMetadata {

    @NotNull(message = Constants.MANDATORY_FIELD)
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "transaction_id", referencedColumnName = "id", nullable = false, unique = true)
    private PaymentTransaction transactionId;

    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "user_id", nullable = false)
    private String userId;

    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "amount_paid", nullable = false)
    private BigDecimal amountPaid;

    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "payment_date", nullable = false)
    private String paymentDate;

    @Enumerated(EnumType.STRING)
    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "payment_mode", nullable = false)
    private PaymentMode paymentMode;

    @Column(name = "payment_provider")
    private String paymentProvider;

    @Column(name = "online_order_id")
    private String onlineOrderId;

    @Column(name = "online_payment_id")
    private String onlinePaymentId;

    @Column(name = "online_signature")
    private String onlineSignature;

    @Column(name = "payment_description")
    private String paymentDescription;

    public enum PaymentMode {
        ONLINE,
        CASH
    }
}
