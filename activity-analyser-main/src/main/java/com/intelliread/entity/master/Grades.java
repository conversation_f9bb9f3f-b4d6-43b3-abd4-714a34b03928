package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@Entity
@Table(name = "grades")
@Cache(region = "grades", usage = CacheConcurrencyStrategy.READ_WRITE)
public class Grades extends AuditMetadata {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "grade")
	private String grade;
	
	@Column(name = "discription")
	private String discription;
	
	// To do the ascending or descending order while fetch all the data.
	@Column(name = "sort_order", unique = true)
	private Integer sortOrder;
}
