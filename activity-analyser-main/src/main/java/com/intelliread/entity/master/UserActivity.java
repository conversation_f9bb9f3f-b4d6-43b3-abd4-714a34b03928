package com.intelliread.entity.master;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.GenericGenerator;

import java.util.UUID;

@Entity(name = "UserActivity")
@Table(name = "ir_user_activity")
public class UserActivity {
    @Id
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @Column(name = "id", nullable = false, updatable = false, unique = true, columnDefinition = "CHAR(32)")
    private String id;

    @Column(name = "user_id", nullable = false)
    private String userId;                 // Foreign Key, references User(id)

    @Column(name = "course_id", nullable = false)
    private String courseId;             // Course ID for which the activity was emitted

    @Column(name = "section_id", nullable = false)
    private String sectionId;            // Section ID for which the activity was emitted

    @Column(name = "chapter_id", nullable = false)
    private String chapterId;            // Chapter ID for which the activity was emitted

    @Column(nullable = false)
    private String createdAt;            // Timestamp of creation

    @Column(nullable = false)
    private String updatedAt;            // Timestamp of last update

    @Column(nullable = false, columnDefinition = "jsonb", name = "activity")
    @ColumnTransformer(write = "?::jsonb")
    private String activity;             // Store ActivityData as JSON String

    @Column(nullable = false, columnDefinition = "jsonb", name = "activityScoreConfig")
    @ColumnTransformer(write = "?::jsonb")
    private String activityScoreConfig;  // Store ActivityScoreConfig as JSON String

    @Column(nullable = false, columnDefinition = "jsonb", name = "userScore")
    @ColumnTransformer(write = "?::jsonb")
    private String userScore;            // Store UserScore as JSON String

    public static class ActivityData {
        private String activityType;         // Activity type (e.g., CHAPTER_COMPLETE, SPEAK_ALOUD, etc.)
        private String startTime;            // ISO-8601 formatted start time
        private String endTime;              // ISO-8601 formatted end time
        private int score;                   // Final calculated score for this activity

        // Getters and Setters
        public String getActivityType() {
            return activityType;
        }

        public void setActivityType(String type) {
            this.activityType = type;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public int getScore() {
            return score;
        }

        public void setScore(int score) {
            this.score = score;
        }
    }

    // ActivityScoreConfig Class
    public static class ActivityScoreConfig {
        private String type;                 // Primary Key, activity type (e.g., CHAPTER_COMPLETE, SPEAK_ALOUD)
        private int rewardThreshold;         // Reward score (e.g., 2, or -2 for penalties)
        private int thresholdTime;           // Threshold time in seconds (e.g., 60 for assessment completion)
        private int defaultReward;

        // Getters and Setters
        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public int getRewardThreshold() {
            return rewardThreshold;
        }

        public void setRewardThreshold(int rewardThreshold) {
            this.rewardThreshold = rewardThreshold;
        }

        public int getThresholdTime() {
            return thresholdTime;
        }

        public void setThresholdTime(int thresholdTime) {
            this.thresholdTime = thresholdTime;
        }

        public int getDefaultReward() {
            return defaultReward;
        }

        public void setDefaultReward(int reward) {
            this.defaultReward = reward;
        }
    }

    // UserScore Class
    public static class UserScore {
        private String id;                     // Primary Key, unique identifier for the user score
        private int totalScore;                // Total score accumulated by the user
        private String lastActivityId;         // Foreign Key, references Activity(id), last activity contributing to the score
        private String lastActivityTime;       // The last occurred activity time
        private String createdAt;              // Timestamp of score creation
        private String updatedAt;              // Timestamp of last score update

        public UserScore () {
            UUID uuid = UUID.randomUUID();
            this.id = uuid.toString();
        }

        // Getters and Setters
        public String getId() {
            return id;
        }

        public int getTotalScore() {
            return totalScore;
        }

        public void setTotalScore(int totalScore) {
            this.totalScore = totalScore;
        }

        public String getLastActivityId() {
            return lastActivityId;
        }

        public void setLastActivityId(String lastActivityId) {
            this.lastActivityId = lastActivityId;
        }

        public String getLastActivityTime() {
            return lastActivityTime;
        }

        public void setLastActivityTime(String lastActivityTime) {
            this.lastActivityTime = lastActivityTime;
        }

        public String getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }

        public String getUpdatedAt() {
            return updatedAt;
        }

        public void setUpdatedAt(String updatedAt) {
            this.updatedAt = updatedAt;
        }
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getSectionId() {
        return sectionId;
    }

    public void setSectionId(String sectionId) {
        this.sectionId = sectionId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public ActivityData getActivity() throws JsonProcessingException  {
        return convertFromJson(this.activity, ActivityData.class);
    }

    public void setActivity(ActivityData activity) throws JsonProcessingException  {
        this.activity = convertToJson(activity);
    }

    public ActivityScoreConfig getActivityScoreConfig() throws JsonProcessingException  {
        return convertFromJson(this.activityScoreConfig, ActivityScoreConfig.class);
    }

    public void setActivityScoreConfig(ActivityScoreConfig activityScoreConfig) throws JsonProcessingException {
        this.activityScoreConfig = convertToJson(activityScoreConfig);
    }

    public UserScore getUserScore() throws JsonProcessingException  {
        return convertFromJson(userScore, UserScore.class);
    }

    public void setUserScore(UserScore userScore) throws JsonProcessingException  {
        this.userScore = convertToJson(userScore);
    }

    public static String convertToJson(Object obj) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(obj);
    }

    public static <T> T convertFromJson(String json, Class<T> clazz) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.readValue(json, clazz);
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
}
