package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@Entity
@Table(name = "sub_topics")
@Where(clause = "deleted=false")
@Cache(region = "sub_topics", usage = CacheConcurrencyStrategy.READ_WRITE)
public class SubTopics extends AuditMetadata {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "sub_topic")
	private String subTopic;	
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "subject_id")
	private Subjects subjects;
}
