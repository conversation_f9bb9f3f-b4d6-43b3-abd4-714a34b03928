package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;

/**
 * The CoordinatorTypes persistence layer
 * <p>
 * Deciding the type of the co-ordination, which is going to assign for a
 * teacher's profile while creating/update teacher details by super-admin or
 * school admin.
 * <p>
 * 
 * @see <a href=
 *      "https://xd.adobe.com/view/89c88a6c-df2c-49eb-a4db-fd947f84a4a5-dc9b/screen/4c496f49-bf4b-40ae-b9ac-f41acf95fc17">Master
 *      Admin > Coordinator Type</a>
 *      <p>
 * @see <a href=
 *      "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/4bc69f07-3e51-42c8-bcdc-0178774abcf1">Super
 *      Admin > Registered Schools >> Branch >>Teacher Id add edit – 2</a>
 *      <p>
 * @see <a href=
 *      "https://xd.adobe.com/view/a61ae1aa-5d5b-4eb2-baac-c136b394af1c-f349/screen/4bc69f07-3e51-42c8-bcdc-0178774abcf1">Azvasa
 *      > Registered Schools >> Branch >>Teacher Id add edit – 2</a>
 */
@Getter
@Setter
@Entity
@ToString
@Table(name = "coordinator_types")
@EqualsAndHashCode(callSuper = false)
@Cache(region = "coordinator_types", usage = CacheConcurrencyStrategy.READ_WRITE)
public class CoordinatorTypes extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "coordinator_type")
	private String coordinatorType;

}
