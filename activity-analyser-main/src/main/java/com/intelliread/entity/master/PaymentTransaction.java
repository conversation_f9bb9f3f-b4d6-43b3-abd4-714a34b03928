package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * PaymentTransaction represents payments (upcoming or past) that a user is responsible for.
 * <p>
 * This entity tracks due payments and ensures the system can identify pending payments requiring clearance.
 * Fields include transaction ID, amount, due date, status, and user association.
 *
 * <AUTHOR>
 * @since 1.0.1
 */

@Getter
@Setter
@ToString
@Entity
@Table(name = "ir_payment_transactions")
public class PaymentTransaction extends AuditMetadata {

    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "user_id", nullable = false)
    private String userId;

    // This would serve as the payment details header, like title
    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "transaction_name", nullable = false)
    private String name;

    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "amount_due", nullable = false)
    private BigDecimal amountDue;

    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "due_date", nullable = false)
    private String dueDate;

    @Enumerated(EnumType.STRING)
    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "payment_status", nullable = false)
    private PaymentStatus paymentStatus;

    @Column(name = "description")
    private String description;

    public enum PaymentStatus {
        PENDING,
        PAID,
        OVERDUE
    }
}
