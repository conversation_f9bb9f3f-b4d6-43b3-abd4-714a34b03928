package com.intelliread.entity.master;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import jakarta.persistence.*;
import org.hibernate.annotations.ColumnTransformer;

import java.util.List;
import java.util.UUID;
import java.util.LinkedList;

@Entity(name = "UserPerformanceReport")
@Table(name = "ir_user_performance_report")
public class UserPerformanceReport {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id; // Primary Key, unique identifier for the report

    @Column(nullable = false)
    private String userId;                // Foreign Key, references User(id)

    @Column(nullable = false)
    private int totalScore;               // Total score accumulated by the user

    @Column(nullable = false)
    private String performanceStatus;     // Status indicating performance (e.g., EXCELLENT, NEEDS_IMPROVEMENT)

    @Column(nullable = false)
    private String courseId;              // Course ID for which the activity was emitted

    @Column(nullable = false)
    private String sectionId;             // Section ID for which the activity was emitted

    @Column(nullable = false)
    private String chapterId;             // Chapter ID for which the activity was emitted

    @Column(columnDefinition = "jsonb", name = "weaknesses")
    @ColumnTransformer(write = "?::jsonb")
    private String weaknesses;            // List of weaknesses associated with the user

    @Column(columnDefinition = "jsonb", name = "strengths")
    @ColumnTransformer(write = "?::jsonb")
    private String strengths;             // List of strengths associated with the user

    @Column(nullable = false)
    private String createdAt;             // Timestamp when the report was generated

    @Column(nullable = false)
    private String updatedAt;             // Timestamp of the last update

    // Nested Class for Weakness
    public static class Weakness {
        private String activityId;        // Activity ID associated with the emitted activity
        private String activityType;      // Activity type (e.g., SPEAK_ALOUD, CHAPTER_COMPLETE)
        private int score;                // Score achieved in the activity
        private String remarks;           // Remarks or feedback about the user's performance
        private String chapterId;
        private String sectionId;

        // Getters and Setters
        public String getActivityId() {
            return activityId;
        }

        public void setActivityId(String activityId) {
            this.activityId = activityId;
        }

        public String getActivityType() {
            return activityType;
        }

        public void setActivityType(String activityType) {
            this.activityType = activityType;
        }

        public int getScore() {
            return score;
        }

        public void setScore(int score) {
            this.score = score;
        }

        public String getRemarks() {
            return remarks;
        }

        public void setRemarks(String remarks) {
            this.remarks = remarks;
        }

        public void setChapterId(String chapterId) {
            this.chapterId = chapterId;
        }

        public String getChapterId() {
            return chapterId;
        }

        public void setSectionId(String sectionId) {
            this.sectionId = sectionId;
        }

        public String getSectionId() {
            return sectionId;
        }
    }

    // Nested Class for Strength
    public static class Strength {
        private String activityId;        // Activity ID associated with the emitted activity
        private String activityType;      // Activity type (e.g., SPEAK_ALOUD, CHAPTER_COMPLETE)
        private int score;                // Score achieved in the activity
        private String remarks;           // Remarks or feedback about the user's performance
        private String chapterId;
        private String sectionId;

        // Getters and Setters
        public String getActivityId() {
            return activityId;
        }

        public void setActivityId(String activityId) {
            this.activityId = activityId;
        }

        public String getActivityType() {
            return activityType;
        }

        public void setActivityType(String activityType) {
            this.activityType = activityType;
        }

        public int getScore() {
            return score;
        }

        public void setScore(int score) {
            this.score = score;
        }

        public String getRemarks() {
            return remarks;
        }

        public void setRemarks(String remarks) {
            this.remarks = remarks;
        }

        public void setChapterId(String chapterId) {
            this.chapterId = chapterId;
        }

        public String getChapterId() {
            return chapterId;
        }

        public void setSectionId(String sectionId) {
            this.sectionId = sectionId;
        }

        public String getSectionId() {
            return sectionId;
        }
    }

    // Getters and Setters for UserPerformanceReport
    public UUID getId() {
        return id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(int totalScore) {
        this.totalScore = totalScore;
    }

    public String getPerformanceStatus() {
        return performanceStatus;
    }

    public void setPerformanceStatus(String performanceStatus) {
        this.performanceStatus = performanceStatus;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getSectionId() {
        return sectionId;
    }

    public void setSectionId(String sectionId) {
        this.sectionId = sectionId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public List<Weakness> getWeaknesses() throws JsonProcessingException {
        if (weaknesses == null || weaknesses.isEmpty()) {
            return new LinkedList<>(); // Return an empty mutable list
        }
        return convertFromJson(weaknesses, new TypeReference<List<Weakness>>(){});
    }

    public void setWeaknesses(List<Weakness> weaknesses) throws JsonProcessingException {
        if (weaknesses == null || weaknesses.isEmpty()) {
            this.weaknesses = "[]"; // Default to an empty JSON array
        } else {
            this.weaknesses = convertToJson(weaknesses);
        }
    }

    public List<Strength> getStrengths() throws JsonProcessingException {
        if (strengths == null || strengths.isEmpty()) {
            return new LinkedList<>();
        }
        return convertFromJson(strengths, new TypeReference<List<Strength>>(){});
    }

    public void setStrengths(List<Strength> strengths) throws JsonProcessingException {
        if (strengths == null || strengths.isEmpty()) {
            this.strengths = "[]";
        } else {
            this.strengths = convertToJson(strengths);
        }
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public static String convertToJson(Object obj) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(obj);
    }

    public static <T> T convertFromJson(String json, TypeReference<T> typeReference) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.readValue(json, typeReference);
    }
}