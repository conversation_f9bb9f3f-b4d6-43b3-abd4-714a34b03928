package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@Entity
@Table(name = "subject_types")
@Cache(region = "subject_types", usage = CacheConcurrencyStrategy.READ_WRITE)
public class SubjectTypes extends AuditMetadata {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "subject_type", unique = true)
	private String subjectType;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "discription", unique = true)
	private String discription;
}
