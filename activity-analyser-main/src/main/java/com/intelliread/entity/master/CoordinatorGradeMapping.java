package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@Entity
@ToString
@Table(name = "coordinator_grade_mapping")
@EqualsAndHashCode(callSuper = false)
@Cache(region = "coordinator_grade_mapping", usage = CacheConcurrencyStrategy.READ_WRITE)
public class CoordinatorGradeMapping extends AuditMetadata {
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "coordinator_type_id")
	private CoordinatorTypes coordinatorTypes;
	
	@NotNull(message = Constants.MANDATORY_FIELD)
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "grade_id")
	private Grades grades;

}
