package com.intelliread.entity.master;

import lombok.*;
import org.hibernate.annotations.ColumnTransformer;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ir_grading_mapping")
public class GradingMapping extends AuditMetadata{

	@Column(name = "ir_levels")
	private String IRLevels;
	
	
	@Column(name = "diagnostic_assessment_url")
	private String diagnosticAssessmentURL;
	
	
	@Column(columnDefinition = "jsonb", name = "level_assignment")
	@ColumnTransformer(write = "?::jsonb")
	private String levelAssignment;
	
}
