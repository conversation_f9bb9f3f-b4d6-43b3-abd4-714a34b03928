package com.intelliread.entity.master;

import com.intelliread.enums.ChapterStatus;
import com.intelliread.utilities.Constants;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Cache;

@Getter
@Setter
@ToString
@Entity
@Table(name = "chapters")
@Cache(region = "chapters", usage = CacheConcurrencyStrategy.READ_WRITE)
@EqualsAndHashCode(callSuper = false)
public class Chapters extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "chapter")
	private String chapter;

	@NotNull(message = Constants.MANDATORY_FIELD)
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "board_id")
	private Boards boards;

	@NotNull(message = Constants.MANDATORY_FIELD)
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "grade_id")
	private Grades grades;

	@NotNull(message = Constants.MANDATORY_FIELD)
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "subject_id")
	private Subjects subjects;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "sub_topic_id")
	private SubTopics subTopics;

	@Column(name = "thumbnail_path")
	private String thumbnailPath;

	@Enumerated(value = EnumType.STRING)
	@Column(name = "chapter_status", length = 32, columnDefinition = "varchar(32) default 'PENDING'")
	private ChapterStatus chapterStatus;

	@Column(name = "approved_by")
	private String approvedBy;

	@Column(name = "approved_at")
	private Long approvedAt;

	@Column(name = "rejected_by")
	private String rejectedBy;

	@Column(name = "rejected_at")
	private Long rejectedAt;
	
	@Column(name = "reason", columnDefinition = "TEXT")
	private String reason;

}
