package com.intelliread.entity.master;

import lombok.*;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnTransformer;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity()
@Table(name = "ir_chapters")
@Cache(region = "ir_chapters", usage = CacheConcurrencyStrategy.READ_WRITE)
public class ChaptersEntity extends AuditMetadata {
	
	@Column(name = "name")
	private String name;
	
	@Column(name = "description")
	private String description;
	
	@Column(name = "type")
	private String type;
	
	@Column(name = "content_type")
	private String contentType;
	
	@Column(name = "content_url")
	private String contentURL;
	
	@Column(name = "sequence")
	private int sequence;
	
	@Column(columnDefinition = "jsonb", name = "assessments")
	@ColumnTransformer(write = "?::jsonb")
	private String assessments;
	
	@Column(name = "created_date")
	private String createdDate;

}
