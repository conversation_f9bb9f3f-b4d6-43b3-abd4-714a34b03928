package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@Entity(name = "Boards")
@Table(name = "boards")
public class Boards extends AuditMetadata {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "board")
	private String board;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "discription")
	private String discription;
}
