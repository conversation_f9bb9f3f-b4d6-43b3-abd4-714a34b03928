package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * StudentPaymentProfile represents the profiles for Business-to-Consumer (B2C) users.
 * <p>
 * This entity holds information related to schools, their address, and contact details for profiles created in the system.
 * <p>
 * Fields include school name, city, address, parent name, and contact information.
 *
 * <AUTHOR>
 * @since 1.0.1
 */
@Getter
@Setter
@ToString
@Entity
@Table(name = "ir_student_payment_profiles")
public class StudentPaymentProfile extends AuditMetadata {

    @NotBlank(message = Constants.MANDATORY_FIELD)
    @Column(name = "ir_user_id", nullable = false, unique = true)
    private String irUserId;

    @NotBlank(message = Constants.MANDATORY_FIELD)
    @Column(name = "ir_user_name", unique = true)
    private String irUserName;

    @NotBlank(message = Constants.MANDATORY_FIELD)
    @Column(name = "ir_study_school_name", nullable = false)
    private String irStudySchoolName;

    @Column(name = "ir_city")
    private String irCity;

    @Column(name = "ir_address")
    private String irAddress;

    @Column(name = "ir_parent_name")
    private String irParentName;

    @Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
    @Column(name = "ir_phone_number")
    private String irPhoneNumber;

    @Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
    @Column(name = "ir_email_id", unique = true)
    private String irEmailId;

    @Column(name = "ir_zip_code")
    private String zipCode;

    @Column(name = "ir_state")
    private String state;

    @Column(name = "country")
    private String country;

    @Column(name = "ir_additional_notes")
    private String irAdditionalNotes;
}
