package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@Entity
@Table(name = "subjects")
//@Cache(region = "subjects", usage = CacheConcurrencyStrategy.READ_WRITE)
public class Subjects extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "subject")
	private String subject;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "subject_type_id")
	private SubjectTypes subjectTypes;

	@Column(name = "hide_sub_topics", columnDefinition = "boolean default true")
	private boolean hideSubtopics;
	
	@Column(name = "skilled_subject", columnDefinition = "boolean default true")
	private boolean skilledSubject;
}
