package com.intelliread.entity.master;

import com.intelliread.enums.GradingModelCategory;
import com.intelliread.utilities.Constants;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.Cache;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ir_grading_model")
@Cache(region = "ir_grading_model", usage = CacheConcurrencyStrategy.READ_WRITE)
public class GradingModel extends AuditMetadata{

	@NotNull(message = Constants.IS_EMPTY)
	@Column(name = "grading_model_name")
	private String gradingModelName;

	@Column(name = "pass_grade")
	private String passGrade;

	@NotNull(message = Constants.IS_EMPTY)
	@Enumerated(EnumType.STRING)
	@Column(name = "category", nullable = false, columnDefinition = "varchar(255) default 'COURSE'")
	private GradingModelCategory category;
	
	@Column(columnDefinition = "jsonb", name = "details")
	@ColumnTransformer(write = "?::jsonb")
	private String details;

}
