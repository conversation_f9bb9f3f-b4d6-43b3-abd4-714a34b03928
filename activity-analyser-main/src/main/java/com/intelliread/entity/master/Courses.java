package com.intelliread.entity.master;

import com.intelliread.enums.CourseMode;
import com.intelliread.enums.CourseStatus;
import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.Cache;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@Entity
@Table(name = "courses")
@Cache(region = "courses", usage = CacheConcurrencyStrategy.READ_WRITE)
public class Courses extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "course_name")
	private String courseName;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "code")
	private String code;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "description")
	private String description;

	@Enumerated(EnumType.STRING)
	@Column(name = "course_status")
	private CourseStatus courseStatus;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "application")
	private String application;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "type")
	private String type;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "category")
	private String category;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "location")
	private String location;

	@Enumerated(EnumType.STRING)
	@Column(name = "course_mode")
	private CourseMode courseMode;

	@Column(columnDefinition = "jsonb", name = "prerequistes")
	@ColumnTransformer(write = "?::jsonb")
	private String prerequisites;

	@Column(columnDefinition = "jsonb", name = "duration")
	@ColumnTransformer(write = "?::jsonb")
	private String duration;

	@Column(name = "image_url")
	private String imageUrl;

	@Column(name = "thumbnail_url")
	private String thumbnailUrl;

	@Column(columnDefinition = "jsonb", name = "current_version")
	@ColumnTransformer(write = "?::jsonb")
	private String currentVersion;

	@Column(name = "created_date")
	private String createdDate;

	@Column(name = "created_user_name")
	private String createdUserName;
	
	@Column(name = "created_by_name")
	private String createdByName;

	@Column(columnDefinition = "jsonb", name = "update_history")
	@ColumnTransformer(write = "?::jsonb")
	private String updateHistory;

	@Column(columnDefinition = "jsonb", name = "approval_history")
	@ColumnTransformer(write = "?::jsonb")
	private String approvalHistory;

	@Column(columnDefinition = "jsonb", name = "version_history")
	@ColumnTransformer(write = "?::jsonb")
	private String versionHistory;

	@Column(columnDefinition = "jsonb", name = "tags")
	@ColumnTransformer(write = "?::jsonb")
	private String tags;

	@Column(columnDefinition = "jsonb", name = "certifcate")
	@ColumnTransformer(write = "?::jsonb")
	private String certifcate;

	@Column(name = "next_course_id")
	private String nextCourseId;

}
