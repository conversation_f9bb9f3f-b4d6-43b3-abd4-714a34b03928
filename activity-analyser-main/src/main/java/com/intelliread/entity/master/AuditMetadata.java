package com.intelliread.entity.master;

import jakarta.persistence.*;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

import java.util.Date;

@Data
@ToString
@MappedSuperclass
public abstract class AuditMetadata {

	@Id
	@GeneratedValue(generator = "uuid")
	@GenericGenerator(name = "uuid", strategy = "uuid")
	@Column(name = "id", nullable = false, updatable = false, unique = true, columnDefinition = "CHAR(32)")
	private String id;

	private Long createdAt;

	private Long modifiedAt;

	private String createdBy;

	private String lastModifiedBy;

	private boolean active;

	private boolean deleted;

	public AuditMetadata() {
		this.setActive(true);
		this.setDeleted(false);
		this.createdAt = new Date().getTime();
		this.modifiedAt = this.createdAt;
	}

}
