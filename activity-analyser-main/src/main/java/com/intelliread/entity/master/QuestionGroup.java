package com.intelliread.entity.master;

import lombok.*;
import org.hibernate.annotations.ColumnTransformer;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ir_question_group")
public class QuestionGroup extends AuditMetadata {
	
		
	@Column(name="group_name")
	private String groupName;
	
	
	@Column(columnDefinition = "jsonb", name = "details")
	@ColumnTransformer(write = "?::jsonb")
	private String details;

}
