package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@Entity
@Table(name = "student_categories")
@Cache(region = "student_categories", usage = CacheConcurrencyStrategy.READ_WRITE)
public class StudentCategories extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "student_category", unique = true)
	private String studentCategory;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "discription")
	private String discription;
}
