package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 0.0.1
 *
 */
@Getter
@Setter
@ToString
@Entity
@Table(name = "role_menu_mapping")
public class RoleMenuMapping extends AuditMetadata {
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "role_id")
	private Roles roles;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "menu_id")
	private Menu menus;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "sub_menu_id")
	private SubMenu submenus;
	
	@NotNull(message = Constants.MANDATORY_FIELD)
	@Column(name = "hamburger_order")
	private Integer hamburgerOrder;

}
