package com.intelliread.entity.master;

import lombok.*;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnTransformer;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ir_assignment_progress")
@Cache(region = "ir_assignment_progress", usage = CacheConcurrencyStrategy.READ_WRITE)
public class AssignmentProgressEntity extends AuditMetadata {
	
	@Column(name = "application")
	private String application;

	@Column(name = "assignee_id")
	private String assigneeId;

	@Column(name = "assignee_role")
	private String assigneeRole;

	@Column(name = "course_code")
	private String courseCode;

	@Column(name = "course_id")
	private String courseId;

	@Column(name = "course_name")
	private String courseName;

	@Column(name = "date_assigned")
	private String dateAssigned;
	
	@Column(columnDefinition = "jsonb", name = "progress_summary")
	@ColumnTransformer(write = "?::jsonb")
	private String progressSummary;
	
	@Column(columnDefinition = "jsonb", name = "assessment_summary")
	@ColumnTransformer(write = "?::jsonb")
	private String assessmentSummary;
	
	@Column(columnDefinition = "jsonb", name = "activities")
	@ColumnTransformer(write = "?::jsonb")
	private String activities;


}
