package com.intelliread.entity.master;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

/**
 * <AUTHOR> |  14 April 2022 
 *
 * Maps subjects to a plan
 *
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = false)
@Entity(name = "plan_template")
//@Cache(region = "plan_template", usage = CacheConcurrencyStrategy.READ_WRITE)
public class PlanTemplate extends AuditMetadata {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "plan_grade_mapping_id")
	private PlanGradesMapping planGradeMapping;	
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "subject_id")
	private Subjects subjects;	
	
	/**
	 * This field indicates whether lesson plan will be a part of plan or not for a teacher
	 */
	@Column(name = "lesson_plan", columnDefinition = "boolean default false")
	private boolean lessonPlan;
	
	/**
	 * This field indicates whether revision module  will be a part of plan or not for a teacher
	 */
	@Column(name = "teacher_revision_module", columnDefinition = "boolean default false")
	private boolean teacherRevisionModule;
	
	
	@Column(name = "rm_lock", columnDefinition = "boolean default false")
	private boolean rmLock;
	
	/**
	 * This field indicates whether assessment module  will be a part of plan or not for a teacher
	 */
	@Column(name = "assessment_module", columnDefinition = "boolean default false")
	private boolean assessmentModule;
	
	/**
	 * This field indicates whether activities  will be a part of plan or not for a teacher
	 */
	@Column(name = "activities", columnDefinition = "boolean default false")
	private boolean activities;
	
	@Column(name = "ws_download", columnDefinition = "boolean default false")
	private boolean wsDownload;
	
	@Column(name = "wb_download", columnDefinition = "boolean default false")
	private boolean wbDownload;
	
	/**
	 * This field indicates whether news stories  will be a part of plan or not for a teacher
	 */
	@Column(name = "news_stories", columnDefinition = "boolean default false")
	private boolean newsStories;
	
	/**
	 * This field indicates whether booklet  will be a part of plan or not for a student
	 */
	@Column(name = "student_booklet", columnDefinition = "boolean default false")
	private boolean studentBooklet;
	
	/**
	 * This field indicates whether revision module  will be a part of plan or not for a student
	 */
	@Column(name = "student_revision_module", columnDefinition = "boolean default false")
	private boolean studentRevisionModule;
}
