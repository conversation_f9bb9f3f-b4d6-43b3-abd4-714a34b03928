package com.intelliread.entity.master;


import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.ColumnTransformer;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Getter
@Setter
@ToString
@Entity
@Table(name = "ir_course_assignments")
public class CourseAssignmentsEntity extends AuditMetadata {
	
	@Column(name = "application")
	private String application;
	
	@Column(name = "assignee_role")
	private String assigneeRole;
	
	@Column(name = "assigned_user_id")
	private String assignedUserId;
	
	@JsonRawValue
	@Column(columnDefinition = "jsonb", name = "courses")
	@ColumnTransformer(write = "?::jsonb")
	private String courses;

}
