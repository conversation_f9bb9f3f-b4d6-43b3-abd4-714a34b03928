package com.intelliread.entity.master;

public class SpeakAloud {
    private String audio; // URL of the audio
    private String sentence;
    private Object outcome; // Outcome of the speech evaluation
    private Integer readingScore; // Score of the speech evaluation

    // Getters and Setters
    public String getAudio() {
        return audio;
    }

    public void setAudio(String audio) {
        this.audio = audio;
    }

    public String getSentence() {
        return sentence;
    }

    public void setSentence(String sentence) {
        this.sentence = sentence;
    }

    public Object getOutcome() {
        return outcome;
    }

    public void setOutcome(Object outcome) {
        this.outcome = outcome;
    }

    public Integer getReadingScore() {
        return readingScore;
    }

    public void setReadingScore(Integer readingScore) {
        this.readingScore = readingScore;
    }
}