package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@Entity
@Table(name = "sections")
@Cache(region = "sections", usage = CacheConcurrencyStrategy.READ_WRITE)
public class Sections extends AuditMetadata {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "section")
	private String section;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "discription")
	private String discription;
}
