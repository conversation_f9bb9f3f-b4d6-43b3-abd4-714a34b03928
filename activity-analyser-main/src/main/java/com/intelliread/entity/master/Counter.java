package com.intelliread.entity.master;

import com.intelliread.utilities.Constants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity
@Table(name = "ir_counters")
public class Counter extends AuditMetadata {

    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "entity", nullable = false, unique = true)
    private String entity;

    @Column(name = "value")
    private Integer value;
}
