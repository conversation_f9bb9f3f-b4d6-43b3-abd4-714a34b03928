package com.intelliread.entity.master;

import lombok.*;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ir_sections")
@Cache(region = "ir_sections", usage = CacheConcurrencyStrategy.READ_WRITE)
public class SectionsEntity extends AuditMetadata{
	
	@Column(name = "name")
	private String name;
	
	@Column(name = "description")
	private String description;
	
	@Column(name = "sequence")
	private int sequence;
	
	@Column(name = "thumbnail_url")
	private String thumbnailURL;
	
	@Column(name = "prerequisites")
	private String prerequisites;

}
