package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import com.intelliread.enums.AdministrationType;
import com.intelliread.enums.Gender;
import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
@ToString
@Entity
@Table(name = "administration")
public class Administration extends AuditMetadata {

	@Column(name = "user_name", unique = true)
	private String userName;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.UNICOD_NAME_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "first_name")
	private String firstName;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.UNICOD_NAME_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "last_name")
	private String lastName;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "email")
	private String email;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "mobile")
	private String mobile;

	@NotNull(message = Constants.IS_EMPTY)
	@Enumerated(EnumType.STRING)
	@Column(name = "gender", nullable = false)
	private Gender gender;

	@Enumerated(EnumType.STRING)
	@Column(name = "administration_type", length = 32, columnDefinition = "varchar(32) default 'SCHOOL_ADMIN'")
	private AdministrationType administrationType;
}
