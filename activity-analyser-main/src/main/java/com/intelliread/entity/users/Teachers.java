package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import com.intelliread.enums.AcademicStaffProfile;
import com.intelliread.enums.Gender;
import com.intelliread.utilities.Constants;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.time.LocalDate;

@Getter
@Setter
@ToString
@Entity
@Table(name = "teachers")
@EqualsAndHashCode(callSuper = false)
public class Teachers extends AuditMetadata {

	@NotBlank(message = Constants.IS_EMPTY)
//	@Pattern(regexp = Constants.UNICOD_NAME_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "first_name")
	private String firstName;

	@NotBlank(message = Constants.IS_EMPTY)
//	@Pattern(regexp = Constants.UNICOD_NAME_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "last_name")
	private String lastName;

	@Column(name = "user_name", unique = true)
	private String userName;

	@NotBlank(message = Constants.IS_EMPTY)
	@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "email")
	private String email;

	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Size(min = 10, max = 10, message = Constants.PHONE_NUMBER_LENGTH)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "mobile")
	private String mobile;

	//@NotNull(message = Constants.IS_EMPTY)
	@Column(name = "dob")
	private LocalDate dob;

	@NotNull(message = Constants.MANDATORY_FIELD)
	@Enumerated(EnumType.STRING)
	@Column(name = "gender", nullable = false)
	private Gender gender;

	//@NotNull(message = Constants.IS_EMPTY)
	@Column(name = "join_date")
	private LocalDate joinDate;

	/**
	 * The Teacher Previous Work Experience field
	 */
	//@NotBlank(message = Constants.IS_EMPTY)
	@Column(name = "previous_work_exp")
	private String previousWorkExp;

	/**
	 * The Teacher Designation field eg., Maths teacher, Physics teacher etc
	 */
	@Column(name = "designation")
	private String designation;

	@Column(name = "role_id")
	private String roleId;

	@Column(name = "document_url")
	private String documentUrl;

	@Enumerated(EnumType.STRING)
	@Column(name = "academic_staff_profile", length = 32, columnDefinition = "varchar(32) default 'TEACHER'")
	private AcademicStaffProfile academicStaffProfile;
	
	@Column(name = "coordinator_type_id")
	private String coordinatorTypeId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "branch_id")
	private Branches branches;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "school_id")
	private Schools schools;

	@Column(name = "address")
	private String address;

}
