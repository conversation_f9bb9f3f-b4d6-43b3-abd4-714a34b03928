package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 1. Users is responsible for login.
 * <p>
 * 2. Users like teacher/student or administration(management id or school
 * admin) created in the system then it has to be mapped here.
 * <p>
 * 3. Users like Admin-Users (Master/super/revision/content Admin &
 * Master/super/revision/content reviewer will be create in this entity)
 * <p>
 * For more about above point(point no: 3)
 * 
 * @see <a href=
 *      "https://xd.adobe.com/view/89c88a6c-df2c-49eb-a4db-fd947f84a4a5-dc9b/screen/1e2b14a7-1d99-4606-9f48-fb04d9bb7e3c/">Add
 *      Admin User</a>
 * 
 * <AUTHOR> Arya C Achari
 */

@Getter
@Setter
@ToString
@Entity
@Table(name = "users")
@Cache(region = "users", usage = CacheConcurrencyStrategy.READ_WRITE)
public class Users extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "user_name", unique = true)
	private String userName;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "password")
	private String password;

	@Column(name = "first_name")
	private String firstName;

	@Column(name = "last_name")
	private String lastName;

	// @NotBlank(message = Constants.MANDATORY_FIELD)
	// @Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "email")
	private String email;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "phone_number")
	private String phoneNumber;

	@Column(name = "last_login_time")
	private Long lastLoginTime;

	@Pattern(regexp = Constants.OTP_FOUR_DIGIT, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "otp")
	private String otp;

	@Column(name = "admin_user", columnDefinition = "boolean default false")
	private boolean adminUser;

	/**
	 * The convert UserRequest to Users
	 */
//	public static Users convertUserRequestToUsers(UsersRequestDto userRequestDto) {
//		Users user = new Users();
//		user.userName = userRequestDto.getUserName();
//		user.password = userRequestDto.getPassword();
//		user.email = userRequestDto.getEmail();
//		user.phoneNumber = userRequestDto.getPhoneNumber();
//		return user;
//	}

	@Column(name = "forget_password_otp_code")
	private String forgetPasswordOtpCode;

	@Column(name = "otp_sended_at")
	private Long otpSendedAt;

	@Column(name = "otp_type")
	private String otpType;

}
