package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

/**
 * Using to get the list of users, while do the self registration by the user.
 * User_Token combination should not be duplicate.
 * 
 * <AUTHOR>
 * <AUTHOR>
 * @since 0.0.1
 * @see {@link Tokens Tokens}
 *
 */
@Getter
@Setter
@ToString
@Entity
@Table(name = "user_token_mapping")
public class UsersTokenMapping extends AuditMetadata {

	@NotNull(message = Constants.MANDATORY_FIELD)
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "user_id")
	private Users users;
	
	@NotNull(message = Constants.MANDATORY_FIELD)
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "token_id")
	private Tokens tokens;
}
