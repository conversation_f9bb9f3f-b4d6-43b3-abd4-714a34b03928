package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import jakarta.validation.constraints.Future;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Tokens generation for a specific school and it's branch
 * 
 * <p>
 * {@code tokenId} is the unique field with the combination of random number and
 * current date, in the format DDMMYYYY. {@code tokenId} can hold one
 * {@code role} and multiple users.
 * 
 * <p>
 * if the value of {@code multiUser} is false then {@code numberOfUsersPerToken}
 * set to 1
 * 
 * <p>
 * {@code numberOfUsersPerToken} means how many users can use one tokenId
 * 
 * <AUTHOR>
 * @since 0.0.1
 *
 */
@Getter
@Setter
@ToString
@Entity
@Table(name = "tokens")
public class Tokens extends AuditMetadata {
	
	@NotNull(message = Constants.MANDATORY_FIELD)
	@Column(name = "token", unique = true)
	private String token;

	@NotNull(message = Constants.MANDATORY_FIELD)
	@Column(name = "number_of_tokens")
	private Integer numberOfTokens;

	/**
	 * roleId from master table roles
	 */
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "role_id")
	private String roleId;

	@NotNull(message = Constants.MANDATORY_FIELD)
	@Future(message = Constants.MANDATORY_FIELD)
	@Column(name = "expiary_date")
	private LocalDate expiaryDate;

	@NotNull(message = Constants.MANDATORY_FIELD)
	@Column(name = "multi_user")
	private boolean multiUser;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "branch_id")
	private Branches branches;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "school_id")
	private Schools schools;

	/**
	 * {@code numberOfUsersPerToken} means how many users can use one token
	 */
	@Column(name = "number_of_users_per_token")
	private Integer numberOfUsersPerToken;
	
	@Column(name = "token_use_count")
	private Integer tokenUseCount;

	@OneToMany(cascade = CascadeType.ALL, mappedBy = "tokens")
	private List<UsersTokenMapping> userTokenMappings = new ArrayList<>();

	

}
