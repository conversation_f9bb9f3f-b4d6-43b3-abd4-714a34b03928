package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import com.intelliread.utilities.Constants;
import lombok.*;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Getter
@Setter
@Entity
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "schools")
@Cache(region = "schools", usage = CacheConcurrencyStrategy.READ_WRITE)
@EqualsAndHashCode(callSuper = false)
public class Schools extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "name")
	private String name;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "code", unique = true)
	private String code;

	/**
	 * cityId from master table cities
	 */
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "city_id")
	private String cityId;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "poc_email")
	private String pocEmail;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "phone_number")
	private String phoneNumber;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.WEBSITE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "website")
	private String website;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "signatory_name")
	private String signatoryName;

	/**
	 * digital signature
	 */
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "signatory_role")
	private String signatoryRole;

	/**
	 * The upload logo url of school
	 */
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "logo_url")
	private String logoUrl;

	@Column(name = "board_id")
	private String boardId;

	private String locality;

	private String planId;

	/**
	 * Check Branch existence , columnDefinition = "boolean default true"
	 */
	@Column(name = "has_branch")
	private boolean hasBranch;

}
