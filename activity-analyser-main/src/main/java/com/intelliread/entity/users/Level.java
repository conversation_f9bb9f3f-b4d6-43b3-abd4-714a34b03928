package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity
@Table(name = "ir_levels")
public class Level extends AuditMetadata {

    @Column(name = "name")
    private String name;

    @Column(name = "value")
    private int value;

    @Column(name = "grade_id")
    private String gradeId;
}
