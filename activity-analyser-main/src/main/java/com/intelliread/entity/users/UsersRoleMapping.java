package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import lombok.*;

import jakarta.persistence.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "users_role_mapping", uniqueConstraints = { @UniqueConstraint(columnNames = { "user_id", "role_id" }) })
public class UsersRoleMapping extends AuditMetadata {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "user_id")
	private Users users;

	/**
	 * roleId from master table roles
	 */
	@Column(name = "role_id")
	private String roleId;


	/**
     * Transient getter for userId from the associated Users entity.
     */
    @java.beans.Transient
    public String getUserId() {
        return users != null ? users.getId() : null;
    }
}
