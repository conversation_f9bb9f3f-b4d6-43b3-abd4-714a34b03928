package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;

@Getter
@Setter
@ToString
@Entity(name ="branch_plan_mappings")
public class BranchPlanMappings extends AuditMetadata {


	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "branch_id")
	private Branches branches;
	
	@Column(name ="plan_id")
	private String planId;
	
	@Column(name ="plan_name")
	private String planName;
}
