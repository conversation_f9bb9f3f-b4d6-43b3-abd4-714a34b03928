package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import com.intelliread.enums.SectionData;
import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@ToString
@Entity
@Table(name = "grade_section_mapping")
@Where(clause = "deleted=false")
public class GradeSectionMapping extends AuditMetadata {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "school_id")
	private Schools schools;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "branch_id")
	private Branches branches;

	/**
	 * PK from master table Grades
	 */
	@Column(name = "grade_id")
	private String gradeId;

	/**
	 * PK from master table Sections
	 */
	@Column(name = "section_id")
	private String sectionId;

	@NotNull(message = Constants.MANDATORY_FIELD)
	@Enumerated(EnumType.STRING)
	@Column(name = "section_data")
	private SectionData sectionData;
	
	/**
	 * PK from master table AcademicYears
	 */
	@Column(name = "academic_year_id")
	private String academicYearId;

}
