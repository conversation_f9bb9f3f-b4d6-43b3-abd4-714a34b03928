package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import com.intelliread.enums.Gender;
import com.intelliread.utilities.Constants;
import lombok.*;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDate;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "students")
@Cache(region = "students", usage = CacheConcurrencyStrategy.READ_WRITE)
public class Students extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
//	@Pattern(regexp = Constants.UNICOD_NAME_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "first_name")
	private String firstName;

	@NotBlank(message = Constants.MANDATORY_FIELD)
//	@Pattern(regexp = Constants.UNICOD_NAME_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "last_name")
	private String lastName;
	
	@Column(name = "user_name", unique = true)
	private String userName;

	//@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "email")
	private String email;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "mobile")
	private String mobile;

//	@NotNull(message = Constants.IS_EMPTY)
	@Enumerated(EnumType.STRING)
	@Column(name = "gender", nullable = false)
	private Gender gender;

	@Column(name = "dob")
	private LocalDate dob;

	/**
	 * PK from master table Languages
	 */
	@Column(name = "first_language_id")
	private String firstLanguageId;
	
	/**
	 * PK from master table Languages
	 */
	@Column(name = "second_language_id")
	private String secondLanguageId;

	@Column(name = "admission_date")
	private LocalDate admissionDate;
	
	/**
	 * PK from master table Grades
	 */
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "grade_id")
	private String gradeId;
	
	/**
	 * PK from master table Sections
	 */
	//@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "section_id")
	private String sectionId;
	
	/**
	 * PK from master table StudentCategories
	 */
	@Column(name = "student_category_id")
	private String studentCategoryId;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "branch_id")
	private Branches branches;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "school_id")
	private Schools schools;
	
	@Column(name = "document_url")
	private String documentUrl; // URL to s3 to save the documents.

	/**
	 * Demote = false
	 * Promote = true
	 * default value null
	 * 
	 */
	@Column(name = "is_promoted")
	private Boolean isPromoted;

	/**
	 * To check the demote and promote happen in the same academic year
	 * 
	 * Promote/demote in the end of academic year = true
	 * Promote/demote in the same academic year = false 
	 * default value null
	 * 
	 */
	@Column(name = "year_end_process")
	private Boolean yearEndProcess;
	
	@Column(name = "address")
	private String address;
	
	@Column(name = "learning_generation")
	private String learningGeneration;
	
	@Column(name = "geographical_type")
	private String geographicalType;
	
	@Column(name = "grade_level")
	private String gradeLevel ;
	
	@Column(name = "ir_status")
	private String irStatus ;
	
	@Column(name = "diagnostic_test")
	private Boolean diagnosticTest ;

}
