package com.intelliread.entity.users;

import com.intelliread.entity.master.AuditMetadata;
import com.intelliread.utilities.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@ToString
@Entity
@Table(name = "student_subject_mapping")
public class StudentSubjectMapping extends AuditMetadata {

    @NotNull(message = Constants.MANDATORY_FIELD)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id")
    private Students student;

    /**
     * PK from master table Grades
     */
    @NotBlank(message = Constants.MANDATORY_FIELD)
    @Column(name = "subject_group_subject_mappings_id")
    private String subjectGroupSubjectMappingsId;

    /**
     * PK from master table Grades
     */
    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "academic_years_id")
    private String academicYearsId;
}
