package com.intelliread.authentication;

import com.intelliread.component.Translator;
import com.intelliread.entity.users.Users;
import com.intelliread.enums.ErrorCodes;
import com.intelliread.exception.MSException;
import com.intelliread.feign.users.UsersFeignClient;
import com.intelliread.feign.users.UsersFeignDto;
import com.intelliread.repository.users.UsersRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> The {@code AuthServiceImpl} implements
 *         {@code UserDetailsService} <br>
 *         {@code @Service} is a stereotypical annotation used for Service Layer
 *         <br>
 *         {@code Slf4j} is a Logger annotation obtained from Lombok dependency
 *         for logging the requests and responses
 */

@Slf4j
@Service("authService")
@RequiredArgsConstructor
public class AuthServiceImpl implements UserDetailsService {

	private final UsersFeignClient usersClient;

	private final UsersRepository usersRepository;

	@Override
	public UserDetails loadUserByUsername(String userName) throws UsernameNotFoundException {
		UsersFeignDto usersDto = getUsersDetails(userName);
		List<SimpleGrantedAuthority> authorities = new ArrayList<>();
		return new User(usersDto.getUserName(), usersDto.getPassword(), authorities);
	}

	/**
	 * Users details get from the users-service by the feign client call.
	 * 
	 * @param userName
	 * @return
	 */
	private UsersFeignDto getUsersDetails(String userName) {
		if (StringUtils.isEmpty(userName)) {
			throw new MSException(ErrorCodes.BAD_REQUEST, Translator.toLocale("user.name.mandatory", null));
		}
		try {
			Users user =  usersRepository.findByUserName(userName);
			return new UsersFeignDto(
					user.getId(),
					user.getUserName(),
					user.getEmail(),
					user.getPhoneNumber(),
					user.getPassword()
			);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new MSException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.not.found", null));
		}
	}
}
