package com.intelliread.authentication;

import jakarta.servlet.Filter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.BeanIds;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.authentication.configurers.userdetails.DaoAuthenticationConfigurer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.channel.ChannelProcessingFilter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Processes an {@link SecurityConfig} request.
 *
 * <AUTHOR> Mandal
 */

@Configuration
@EnableWebSecurity
public class SecurityConfig {

	/**
	 * Authentication Service Implemented Configuration Build
	 *
	 */
	@Autowired
	private AuthServiceImpl authService;

	/**
	 * JwtFilter doFilterInternal
	 *
	 */
	@Autowired
	private JwtFilter jwtFilter;

	@Autowired
	private CorsFilter corsFilter;

	/**
	 * Add authentication based upon the custom {@link UserDetailsService} that is
	 * passed in. It then returns a {@link DaoAuthenticationConfigurer} to allow
	 * customization of the authentication.
	 *
	 * @throws Exception if an error occurs when adding the
	 *                   {@link UserDetailsService} based authentication
	 */
	protected void configure(AuthenticationManagerBuilder auth) throws Exception {
		auth.userDetailsService(authService);
	}

	/**
	 * Encode the raw password. Generally, a good encoding algorithm applies a SHA-1
	 * or greater hash combined with an 8-byte or greater randomly generated salt.
	 */
	@Bean
	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder();
	}

	/**
	 * Processes an {@link Authentication} request.
	 */
	@Bean(name = BeanIds.AUTHENTICATION_MANAGER)
	public AuthenticationManager authenticationManagerBean(AuthenticationConfiguration authConfig) throws Exception {
		return authConfig.getAuthenticationManager();
	}

	@Bean
	public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
		return http.csrf(AbstractHttpConfigurer::disable)
				.authorizeHttpRequests(authz -> authz
						.requestMatchers(jwtFilter.UNAUTHENTICATED_ROUTES.toArray(new String[0])).permitAll()
						.anyRequest().authenticated()
				)
				.sessionManagement(session -> session
						.sessionCreationPolicy(SessionCreationPolicy.STATELESS)
				)
				.addFilterBefore(corsFilter, ChannelProcessingFilter.class)
				.addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class)
				.build();
	}

}
