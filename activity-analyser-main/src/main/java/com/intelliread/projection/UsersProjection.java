package com.intelliread.projection;

import java.time.LocalDateTime;

public interface UsersProjection {
	
	public String getId();
	public String getName();
	public String getFirstName();
	public String getLastName();
	public String getUserName();
	public String getEmail();
	public String getPhoneNumber();
	public LocalDateTime getLastLoginTime();
	public boolean isActive();
	public String getPassword();
	public String getOtp();
	public boolean isAdminUser();
	public LocalDateTime getCreatedAt();
	public String getCreatedBy();
	public LocalDateTime getModifiedAt();
	public String getLastModifiedBy();
	public boolean isDeleted();
}