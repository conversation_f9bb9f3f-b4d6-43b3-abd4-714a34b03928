package com.intelliread.projection;

import java.time.LocalDate;
import java.time.LocalDateTime;

public interface TokenListProjection {
	
	public String getId();

	public String getToken();

	public String getRoleId();

	public String getEmail();

	public String getMultiUser();

	public Integer getNoOfUsers();
	
	public String getUsedUserId();

	public LocalDateTime getDateCreated();

	public LocalDate getExpiaryDate();
	
	public String getBranchId();

	public String getBranch();

	public String getSchoolId();

	public String getSchool();
}
