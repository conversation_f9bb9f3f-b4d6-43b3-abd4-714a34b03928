package com.intelliread.projection;

public interface GradesProjection {

	public String getId();

	public String getGrade();

	public int getSortOrder();

	public String getDiscription();

	public boolean isActive();

	public long getCreatedAt();

	/**
	 * Based on the plan_grade do not use this for other purpose
	 * 
	 * To show the grades from entire system which has connected with a particular
	 * plan. Give the status if all features are enabled true, few feature false and
	 * nothing selected null
	 * 
	 * <AUTHOR> C Achari | 12 Sep 2023
	 *
	 */
	public Boolean getStatus();
}
