package com.intelliread.projection;

public interface SubjectsProjection {

	public String getId();

	public String getSubject();

	public boolean isHideSubtopics();
	
	public boolean isSkilledSubject();

	public String getSubjectTypeId();

	public String getSubjectType();

	public String getDiscription();

	public boolean isActive();

	public long getCreatedAt();
	
	public String getSubTopicId();
	
	public String getSubTopic();	
}
